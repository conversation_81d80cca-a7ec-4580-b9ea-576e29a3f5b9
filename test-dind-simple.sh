#!/bin/bash
set -e

echo "=== 简单 DinD 路径测试 ==="
echo "当前目录: $(pwd)"
echo "CI_WORKSPACE: ${CI_WORKSPACE:-未设置}"

# 检查脚本文件
echo "检查本地脚本文件:"
ls -la ops/scripts/ || {
    echo "❌ 本地脚本目录不存在"
    exit 1
}

# 设置 CI_WORKSPACE 如果未设置
if [ -z "$CI_WORKSPACE" ]; then
    export CI_WORKSPACE=$(pwd)
    echo "设置 CI_WORKSPACE 为: $CI_WORKSPACE"
fi

# 测试 Docker 容器访问
echo "测试 Docker 容器访问脚本:"
docker run --rm -v "$CI_WORKSPACE/ops/scripts:/test-scripts:ro" alpine sh -c "
    echo '容器内脚本目录:';
    ls -la /test-scripts/ || echo '无法访问脚本目录';
    if [ -f '/test-scripts/init-dogecoin-wallet.sh' ]; then
        echo '✅ Dogecoin 脚本可访问';
    else
        echo '❌ Dogecoin 脚本不可访问';
    fi;
    if [ -f '/test-scripts/init-litecoin-wallet.sh' ]; then
        echo '✅ Litecoin 脚本可访问';
    else
        echo '❌ Litecoin 脚本不可访问';
    fi
"

echo "测试完成！" 