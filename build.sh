#!/bin/bash

set -e

docker stop mining-pool || true
docker rm mining-pool || true
docker rmi mining-pool || true

echo "Building Go application..."
go mod tidy
#go build -o dogepool .
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o dogepool .

if [ ! -f "./dogepool" ]; then
    echo "Build failed: dogepool binary not found."
    exit 1
fi

echo "Building Docker image..."
docker build -t coinflow/mining-pool:latest .