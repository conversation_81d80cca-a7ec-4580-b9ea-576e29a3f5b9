# Woodpecker CI - 简洁测试流程
when:
  - branch: main
    event: [push, pull_request]


volumes:
  go-mod-cache:
    driver: local
  go-build-cache:
    driver: local

workspace:
  path: /tmp/shared-workspace


steps:
  # 单元测试
  - name: unit-test
    image: golang:1.23.0
    environment:
      CGO_ENABLED: 0
    volumes:
      - go-mod-cache:/go/pkg/mod
      - go-build-cache:/root/.cache/go-build
    commands:
      - echo "=== Unit Test Environment ==="
      - echo "Go version $(go version)"
      - echo "CGO_ENABLED $CGO_ENABLED"
      - go mod tidy
      - go test -v ./tests/unit/*.go

  # 集成测试 - 使用优化的 DinD 文件访问策略
  - name: integration-test
    image: coinflow/dind-go
    network_mode: host
    privileged: true
    environment:
      DOCKER_HOST: unix:///var/run/docker.sock
      CI: true
      SHARED_WORKSPACE: /tmp/shared-workspace
      CGO_ENABLED: 0
    volumes:
      - go-mod-cache:/go/pkg/mod
      - go-build-cache:/root/.cache/go-build
      - /var/run/docker.sock:/var/run/docker.sock
      # 优化策略：直接映射工作目录到宿主机共享路径
      - /tmp/shared-workspace:/tmp/shared-workspace
    commands:
      - |
        # 设置清理陷阱
        trap 'echo "Cleaning up Docker services..."; make docker-down || true' EXIT

        make docker-up
        go test -v ./tests/integrat/

    depends_on:
      - unit-test