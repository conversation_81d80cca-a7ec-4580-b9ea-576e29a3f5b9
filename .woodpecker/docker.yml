# Woodpecker CI - Docker 构建流程

when:
  event: manual


volumes:
  docker_cache:
    driver: local

steps:
  # 构建并推送主应用 Docker 镜像
  - name: build-docker-pool
    image: woodpeckerci/plugin-docker-buildx
    volumes:
      - docker_cache:/cache
    settings:
      repo: coinflow/mining-pool
      dockerfile: Dockerfile
      context: .
      platforms:
        - linux/amd64
        - linux/arm64
      tags:
        - latest
      username:
        from_secret: DOCKERHUB_USERNAME
      password:
        from_secret: DOCKERHUB_TOKEN
      cache_from:
        - 'type=local\,src=/cache'
      cache_to: 
        - 'type=local,dest=/cache,mode=max'
     

  # 构建并推送矿工 Docker 镜像
  - name: build-docker-miner
    image: woodpeckerci/plugin-docker-buildx
    volumes:
      - docker_cache:/cache
    settings:
      repo: coinflow/mining-pool-miner
      dockerfile: Dockerfile-miner
      context: .
      platforms:
        - linux/amd64
        - linux/arm64
      tags:
        - latest
      username:
        from_secret: DOCKERHUB_USERNAME
      password:
        from_secret: DOCKERHUB_TOKEN
      cache_from:
        - 'type=local\,src=/cache'
      cache_to:
        - "type=local,dest=/cache,mode=max"