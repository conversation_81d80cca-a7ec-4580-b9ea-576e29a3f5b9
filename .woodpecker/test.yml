# Woodpecker CI - 简洁测试流程
when:
  - branch: main
    event: [push, pull_request]


volumes:
  go-mod-cache:
    driver: local
  go-build-cache:
    driver: local

workspace:
  path: /tmp/shared-workspace


steps:
  # 单元测试
  - name: unit-test
    image: golang:1.23.0
    environment:
      CGO_ENABLED: 0
    volumes:
      - go-mod-cache:/go/pkg/mod
      - go-build-cache:/root/.cache/go-build
    commands:
      - echo "=== Unit Test Environment ==="
      - echo "Go version $(go version)"
      - echo "CGO_ENABLED $CGO_ENABLED"
      - go mod tidy
      - go test -v ./tests/unit/*.go

  # 集成测试 - 使用优化的 DinD 文件访问策略
  - name: integration-test
    image: coinflow/dind-go
    network_mode: host
    privileged: true
    environment:
      DOCKER_HOST: unix:///var/run/docker.sock
      CI: true
      SHARED_WORKSPACE: /tmp/shared-workspace
      CGO_ENABLED: 0
    volumes:
      - go-mod-cache:/go/pkg/mod
      - go-build-cache:/root/.cache/go-build
      - /var/run/docker.sock:/var/run/docker.sock
      # 优化策略：直接映射工作目录到宿主机共享路径
      - /tmp/shared-workspace:/tmp/shared-workspace
    commands:
      - |
        # 设置清理陷阱
        trap 'echo "Cleaning up Docker services..."; cd ops && SHARED_WORKSPACE="$SHARED_WORKSPACE" docker-compose down -v || true' EXIT

        # 准备测试环境
        echo "=== Preparing test environment ==="
        echo "Go version $(go version)"
        echo "CGO_ENABLED $CGO_ENABLED"
        echo "SHARED_WORKSPACE: $SHARED_WORKSPACE"
        # 步骤1: 检查CI容器内的目录结构
        echo "=== Step 1: Check CI container directory structure ==="
        echo "Current working directory: $(pwd)"
        echo "SHARED_WORKSPACE: $SHARED_WORKSPACE"
        echo "Checking ops/ltcdata directory:"
        ls -la ops/ltcdata/ || echo "ops/ltcdata directory not found"
        echo "Checking ops/ltcdata/node1 directory:"
        ls -la ops/ltcdata/node1/ || echo "ops/ltcdata/node1 directory not found"

        # 步骤2: 检查宿主机共享目录
        echo "=== Step 2: Check host shared directory ==="
        echo "Testing host access to shared ltcdata directory:"
        docker run --rm -v "$SHARED_WORKSPACE/ops/ltcdata:/test-ltcdata" alpine ls -la /test-ltcdata/ || echo "Failed to access shared ltcdata"
        echo "Testing host access to node1 directory:"
        docker run --rm -v "$SHARED_WORKSPACE/ops/ltcdata/node1:/test-node1" alpine ls -la /test-node1/ || echo "Failed to access shared node1"

        # 步骤3: 快速测试docker run映射
        echo "=== Step 3: Quick docker run mapping test ==="
        echo "Testing direct docker run with volume mapping:"
        docker run --rm -v "$SHARED_WORKSPACE/ops/ltcdata/node1:/opt/litecoin/.litecoin" alpine ls -la /opt/litecoin/.litecoin/ || echo "Direct mapping test failed"


        # 启动 Docker Compose 服务
        echo "=== Starting Docker Compose services ==="
        echo "Current working directory: $(pwd)"
        echo "Checking if ops directory exists:"
        ls -la ops/ || echo "ops directory not found"
        echo "Passing SHARED_WORKSPACE=$SHARED_WORKSPACE to docker-compose"
        echo "Expected path should be: $SHARED_WORKSPACE"
        cd ops && SHARED_WORKSPACE="$SHARED_WORKSPACE" docker-compose up 
        cd ..

        # 验证容器启动状态
        echo "=== Verifying container startup ==="
        echo "Checking container status:"
        docker ps -a
        echo "Checking litecoin-node1 logs:"
        docker logs litecoin-node1 || echo "Failed to get container logs"
        echo "Checking mounted directory in container:"
        docker exec litecoin-node1 ls -la /opt/litecoin/.litecoin/ || echo "Failed to check mounted directory"

        # 清理并重新下载依赖
        go mod tidy
        go mod download


        # 等待服务启动
        echo "Waiting for services to be ready..."
        sleep 10

        # 运行集成测试
        echo "=== Running integration tests ==="
        echo "Current working directory: $(pwd)"
        echo "Listing test files: $(ls -la tests/integrat/ || echo 'Directory not found')"
        go test -v ./tests/integrat/

    depends_on:
      - unit-test