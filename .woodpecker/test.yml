# Woodpecker CI - 简洁测试流程
when:
  - branch: main
    event: [push, pull_request]


volumes:
  docker_cache:
    driver: local

workspace:
  path: /tmp/dogehash/mining-pool


steps:
  # 单元测试
  - name: unit-test
    image: golang:1.23.0
    environment:
      CGO_ENABLED: 0
    volumes:
      - go-mod-cache:/go/pkg/mod
      - go-build-cache:/root/.cache/go-build
    commands:
      - echo "=== Unit Test Environment ==="
      - echo "Go version $(go version)"
      - echo "CGO_ENABLED $CGO_ENABLED"
      - go mod tidy
      - go test -v ./tests/unit/*.go

  # 集成测试 - 使用优化的 DinD 文件访问策略
  - name: integration-test
    image: coinflow/dind-go
    network_mode: host
    privileged: true
    environment:
      DOCKER_HOST: unix:///var/run/docker.sock
      CI: true
      SHARED_WORKSPACE: /tmp/dogehash/mining-pool
      CGO_ENABLED: 0
    volumes:
      - go-mod-cache:/go/pkg/mod
      - go-build-cache:/root/.cache/go-build
      - /var/run/docker.sock:/var/run/docker.sock
      # 优化策略：直接映射工作目录到宿主机共享路径
      - /tmp/dogehash/mining-pool:/tmp/dogehash/mining-pool
    commands:
      - |
        # 设置清理陷阱
        trap 'echo "Cleaning up Docker services..."; cd ops && docker-compose down -v || true' EXIT

        # 准备测试环境
        echo "=== Preparing test environment ==="
        echo "Go version $(go version)"
        echo "CGO_ENABLED $CGO_ENABLED"
        echo "SHARED_WORKSPACE: $SHARED_WORKSPACE"

        # 清理并重新下载依赖
        go mod tidy
        go mod download

        # 启动 Docker Compose 服务
        echo "=== Starting Docker Compose services ==="
        cd ops && ./scripts/setup-docker.sh && docker-compose up 
        cd ..

        # 等待服务启动
        echo "Waiting for services to be ready..."
        sleep 10

        # 运行集成测试
        echo "=== Running integration tests ==="
        echo "Current working directory: $(pwd)"
        echo "Listing test files: $(ls -la tests/integrat/ || echo 'Directory not found')"
        go test -v ./tests/integrat/

    depends_on:
      - unit-test
 
