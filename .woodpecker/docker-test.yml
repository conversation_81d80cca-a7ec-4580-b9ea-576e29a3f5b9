when:
  event: manual

steps:
  - name: simple-test
    image: cruizba/ubuntu-dind:jammy-latest
    privileged: true
    environment:
      SHARED_WORKSPACE: /tmp/shared-workspace
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      
      - /tmp/shared-workspace:/workspace
    working_dir: /workspace
    commands:
      - echo "=== Step 1 Check environment and variables ==="
      - echo "Current working directory $(pwd)"
      - echo "SHARED_WORKSPACE variable $SHARED_WORKSPACE"
      - echo "=== Step 2 Check code in CI container ==="
      - echo "CI container contents:"
      - ls -la
      - echo "Check scripts directory:"
      - ls -la scripts/ || echo "No scripts directory"
      - echo "=== Step 3 Verify host can access shared directory ==="
      - echo "View shared directory from host perspective:"
      - docker run --rm -v "$SHARED_WORKSPACE:/workspace" alpine ls -la /workspace/
      - echo "Check scripts directory on host:"
      - docker run --rm -v "$SHARED_WORKSPACE:/workspace" alpine ls -la /workspace/scripts/ || echo "No scripts directory on host"
      - echo "=== Step 4 Docker-in-Docker access test ==="
      - echo "4.1 Mount scripts via shared directory:"
      - docker run --rm -v "$SHARED_WORKSPACE/scripts:/scripts" alpine ls -la /scripts/ || echo "DinD shared scripts mount failed"
      - echo "4.2 Test specific file access:"
      - docker run --rm -v "$SHARED_WORKSPACE/scripts:/scripts" alpine ls -la /scripts/init-dogecoin-wallet.sh || echo "DinD init-dogecoin-wallet.sh not found"
      - echo "4.3 Test file content:"
      - docker run --rm -v "$SHARED_WORKSPACE/scripts:/scripts" alpine sh -c "if [ -f '/scripts/init-dogecoin-wallet.sh' ]; then echo 'File accessible!'; head -3 /scripts/init-dogecoin-wallet.sh; else echo 'File not accessible'; fi"
     
      
    