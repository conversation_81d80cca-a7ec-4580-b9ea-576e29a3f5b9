# Woodpecker CI - PostgreSQL 测试流程（基于优化的 DinD 文件访问策略）
when:
  - branch: main
    event: [push, pull_request]

steps:
  - name: postgresql-test
    image: coinflow/dind-go
    network_mode: host
    privileged: true
    environment:
      DOCKER_HOST: unix:///var/run/docker.sock
      SHARED_WORKSPACE: /tmp/shared-workspace
      CGO_ENABLED: 0
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      # 优化策略：直接映射工作目录到宿主机共享路径
      - /tmp/shared-workspace:/workspace
    working_dir: /workspace
    commands:
      - |
        # 设置清理陷阱
        trap 'echo "Cleaning up Docker services..."; cd ops && docker-compose -f docker-compose-pg.yml down -v || true' EXIT

        # 步骤1: 检查环境变量和工作目录
        echo "=== Step 1: Check environment and variables ==="
        echo "Current working directory: $(pwd)"
        echo "SHARED_WORKSPACE variable: $SHARED_WORKSPACE"

        # 步骤2: 验证 CI 容器内的代码
        echo "=== Step 2: Check code in CI container ==="
        echo "CI container contents:"
        ls -la
        echo "Check ops/schema directory:"
        ls -la ops/schema/ || echo "No schema directory"
        echo "Schema file content:"
        cat ops/schema/01_test_table.sql || echo "Schema file not found"

        # 步骤3: 验证文件映射策略
        echo "=== Step 3: Verify file mapping strategy ==="
        echo "Testing relative path mapping from CI container:"
        cd ops
        echo "Current directory: $(pwd)"
        echo "Schema directory contents:"
        ls -la schema/
        echo "Testing docker volume mount with relative path:"
        docker run --rm -v "$(pwd)/schema:/test-schema" alpine ls -la /test-schema/
        cd ..

        # 步骤4: 启动 PostgreSQL 并测试
        echo "=== Step 4: PostgreSQL service test ==="
        cd ops  && docker-compose -f docker-compose-pg.yml up -d && cd ..

        # 验证容器内的初始化脚本
        echo "=== Verifying container initialization scripts ==="
        sleep 5
        echo "Checking mounted init scripts in PostgreSQL container:"
        docker exec postgres ls -la /docker-entrypoint-initdb.d/ || echo "Failed to list init scripts"
        echo "PostgreSQL initialization logs:"
        docker logs postgres | grep -i "init\|schema\|sql" || echo "No initialization logs found"

        # 等待并验证 PostgreSQL 服务
        echo "=== Waiting for PostgreSQL service ==="
        sleep 10

        # 验证容器健康状态
        if docker exec postgres pg_isready -U root -d pool; then
          echo "✅ PostgreSQL database connection verified"

          # 验证 schema 初始化
          echo "=== Verifying schema initialization ==="
          if docker exec postgres psql -U root -d pool -c "SELECT COUNT(*) FROM test_table;"; then
            echo "✅ Test table exists and schema initialization successful"
            docker exec postgres psql -U root -d pool -c "SELECT id, name FROM test_table ORDER BY id;"
          else
            echo "❌ Schema initialization failed"
            docker exec postgres psql -U root -d pool -c "\dt"
            exit 1
          fi
        else
          echo "❌ PostgreSQL connection failed"
          exit 1
        fi