# Woodpecker CI - 简洁测试流程
when:
  - branch: main
    event: [push, pull_request]


volumes:
  docker_cache:
    driver: local

steps:
  
  # 集成测试 - 使用优化的 DinD 文件访问策略
  - name: integration-test
    image: coinflow/dind-go
    network_mode: host
    privileged: true
    environment:
      DOCKER_HOST: unix:///var/run/docker.sock
      CI: true
      SHARED_WORKSPACE: /tmp/shared-workspace
      CI_WORKSPACE: /workspace
      CGO_ENABLED: 0
    volumes:
      - go-mod-cache:/go/pkg/mod
      - go-build-cache:/root/.cache/go-build
      - /var/run/docker.sock:/var/run/docker.sock
      # 优化策略：直接映射工作目录到宿主机共享路径
      - /tmp/shared-workspace:/workspace
    working_dir: /workspace
    commands:
      - |
        # 设置清理陷阱
        trap 'echo "Cleaning up Docker services..."; cd ops && docker-compose -f docker-compose-pg.yml down -v || true' EXIT

        # 准备测试环境
        echo "=== Preparing test environment ==="
        echo "Go version $(go version)"
        echo "CGO_ENABLED $CGO_ENABLED"

        # 启动 Docker Compose 服务
        echo "=== Starting PostgreSQL service ==="
        cd ops && ./scripts/setup-docker.sh && docker-compose -f docker-compose-pg.yml up -d  && cd ..

        # 等待并测试 PostgreSQL 数据库连接
        echo "=== Testing PostgreSQL database connectivity ==="

        # 检查容器状态
        echo "Checking PostgreSQL container status..."
        cd ops && docker-compose -f docker-compose-pg.yml ps && cd ..

        # 测试数据库连接 - 5次尝试，每次间隔5秒
        i=1
        while [ $i -le 5 ]; do
          echo "Database connectivity test $i/5..."

          # 检查容器健康状态
          health_status=$(docker inspect postgres --format='{{.State.Health.Status}}' 2>/dev/null || echo "unknown")
          echo "Container health status: $health_status"

          if [ "$health_status" = "healthy" ]; then
            echo "✅ PostgreSQL container is healthy"

            # 进一步验证数据库连接（直接在容器内执行）
            if docker exec postgres pg_isready -U root -d pool > /dev/null 2>&1; then
              echo "✅ PostgreSQL database connection verified on attempt $i"
              echo "=== PostgreSQL service is ready ==="
              exit 0
            else
              echo "⚠️ Container is healthy but pg_isready failed"
            fi
          else
            echo "❌ Container health check failed: $health_status"
          fi

          if [ $i -eq 5 ]; then
            echo "💥 All 5 database connectivity tests failed. PostgreSQL service is not ready."
            echo "Final container status check:"
            cd ops && docker-compose -f docker-compose-pg.yml ps && cd ..
            echo "Final health status: $(docker inspect postgres --format='{{.State.Health.Status}}' 2>/dev/null || echo 'unknown')"
            exit 1
          fi

          echo "Waiting 5 seconds before next attempt..."
          sleep 5
          i=$((i + 1))
        done