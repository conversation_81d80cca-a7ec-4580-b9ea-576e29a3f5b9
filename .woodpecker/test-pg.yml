# Woodpecker CI - PostgreSQL 测试流程（基于优化的 DinD 文件访问策略）
when:
  - branch: main
    event: [push, pull_request]

steps:
  - name: postgresql-test
    image: coinflow/dind-go
    network_mode: host
    privileged: true
    environment:
      DOCKER_HOST: unix:///var/run/docker.sock
      SHARED_WORKSPACE: /tmp/shared-workspace
      CGO_ENABLED: 0
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      # 优化策略：将 Woodpecker 代码目录映射到宿主机共享路径
      - /tmp/shared-workspace:/tmp/shared-workspace
    commands:
      - |
        # 设置清理陷阱
        trap 'echo "Cleaning up Docker services..."; cd ops && docker-compose -f docker-compose-pg.yml down -v || true' EXIT

        # 步骤1: 检查环境变量和工作目录
        echo "=== Step 1: Check environment and variables ==="
        echo "Current working directory: $(pwd)"
        echo "SHARED_WORKSPACE variable: $SHARED_WORKSPACE"

        # 将当前代码目录复制到共享路径
        echo "Copying current code to shared workspace:"
        mkdir -p "$SHARED_WORKSPACE"
        cp -r . "$SHARED_WORKSPACE/"
        echo "Shared workspace contents:"
        ls -la "$SHARED_WORKSPACE/"

        # 步骤2: 验证共享路径中的代码
        echo "=== Step 2: Check code in shared workspace ==="
        echo "Shared workspace contents:"
        ls -la "$SHARED_WORKSPACE/"
        echo "Check ops/schema directory in shared workspace:"
        ls -la "$SHARED_WORKSPACE/ops/schema/" || echo "No schema directory"
        echo "Schema file content:"
        cat "$SHARED_WORKSPACE/ops/schema/01_test_table.sql" || echo "Schema file not found"

        # 步骤3: 验证宿主机可以访问共享目录
        echo "=== Step 3: Verify host can access shared directory ==="
        echo "Testing host access to shared schema directory:"
        docker run --rm -v "$SHARED_WORKSPACE/ops/schema:/test-schema" alpine ls -la /test-schema/
        echo "Testing schema file content from host:"
        docker run --rm -v "$SHARED_WORKSPACE/ops/schema:/test-schema" alpine cat /test-schema/01_test_table.sql

        # 步骤4: 启动 PostgreSQL 并测试
        echo "=== Step 4: PostgreSQL service test ==="
        echo "Starting PostgreSQL from shared workspace:"
        cd "$SHARED_WORKSPACE/ops" && ./scripts/setup-docker.sh && docker-compose -f docker-compose-pg.yml up -d && cd -

        # 验证容器内的初始化脚本
        echo "=== Verifying container initialization scripts ==="
        sleep 5
        echo "Checking mounted init scripts in PostgreSQL container:"
        docker exec postgres ls -la /docker-entrypoint-initdb.d/ || echo "Failed to list init scripts"
        echo "Checking init script content in container:"
        docker exec postgres cat /docker-entrypoint-initdb.d/01_test_table.sql || echo "Failed to read init script"
        echo "PostgreSQL initialization logs:"
        docker logs postgres | grep -i "init\|schema\|sql\|CREATE\|INSERT" || echo "No initialization logs found"

        # 等待并验证 PostgreSQL 服务
        echo "=== Waiting for PostgreSQL service ==="
        sleep 10

        # 验证容器健康状态
        if docker exec postgres pg_isready -U root -d pool; then
          echo "✅ PostgreSQL database connection verified"

          # 验证 schema 初始化
          echo "=== Verifying schema initialization ==="
          if docker exec postgres psql -U root -d pool -c "SELECT COUNT(*) FROM test_table;"; then
            echo "✅ Test table exists and schema initialization successful"
            docker exec postgres psql -U root -d pool -c "SELECT id, name FROM test_table ORDER BY id;"
          else
            echo "❌ Schema initialization failed"
            docker exec postgres psql -U root -d pool -c "\dt"
            exit 1
          fi
        else
          echo "❌ PostgreSQL connection failed"
          exit 1
        fi