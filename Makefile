# Mining Pool Makefile

.PHONY: help
help: ## 显示帮助信息
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# Go 命令
.PHONY: build
build: ## 构建应用
	go mod tidy
	CGO_ENABLED=0 go build -o mining-pool .

.PHONY: test
test: ## 运行单元测试
	go test -v ./tests/unit/*.go

.PHONY: test-integration
test-integration: ## 运行集成测试
	go test -v ./tests/integrat/*.go

.PHONY: clean
clean: ## 清理构建文件
	rm -f mining-pool
	go clean -cache


.PHONY: docker-setup
docker-setup: ## 设置 Docker 环境
	cd ops && ./scripts/setup-docker.sh

.PHONY: docker-up
docker-up: docker-setup ## 启动 Docker 服务
	cd ops && docker-compose up 

.PHONY: docker-down
docker-down: ## 停止 Docker 服务
	cd ops && docker-compose down -v

.PHONY: docker-logs
docker-logs: ## 查看服务日志
	cd ops && docker-compose logs -f

.PHONY: docker-status
docker-status: ## 检查服务状态
	cd ops && docker-compose ps
