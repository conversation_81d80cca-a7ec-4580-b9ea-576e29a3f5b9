# 网络拓扑分析

## 概述

当前的 Docker Compose 配置创建了两个独立的区块链测试网络：
1. **Litecoin Regtest Network** - 独立的 Litecoin 测试网络
2. **Dogecoin Regtest Network** - 独立的 Dogecoin 测试网络

## 网络架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    Docker Network                           │
│                 network-mining-pool                         │
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  Litecoin Net   │    │  Dogecoin Net   │                │
│  │                 │    │                 │                │
│  │ ┌─────────────┐ │    │ ┌─────────────┐ │                │
│  │ │litecoin-    │ │    │ │dogecoin-    │ │                │
│  │ │node1        │◄┼────┼►│node1        │ │                │
│  │ │:19332       │ │    │ │:44555       │ │                │
│  │ └─────────────┘ │    │ └─────────────┘ │                │
│  │        ▲        │    │        ▲        │                │
│  │        │        │    │        │        │                │
│  │        ▼        │    │        ▼        │                │
│  │ ┌─────────────┐ │    │ ┌─────────────┐ │                │
│  │ │litecoin-    │ │    │ │dogecoin-    │ │                │
│  │ │node2        │ │    │ │node2        │ │                │
│  │ │:19334       │ │    │ │:44557       │ │                │
│  │ └─────────────┘ │    │ └─────────────┘ │                │
│  │                 │    │                 │                │
│  │ ┌─────────────┐ │    │ ┌─────────────┐ │                │
│  │ │litecoin-cli │ │    │ │dogecoin-cli │ │                │
│  │ │(wallet init)│ │    │ │(wallet init)│ │                │
│  │ └─────────────┘ │    │ └─────────────┘ │                │
│  └─────────────────┘    └─────────────────┘                │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐│
│  │                Mining Pool                              ││
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    ││
│  │  │mining-pool  │  │   miner1    │  │   miner2    │    ││
│  │  │:3643        │  │             │  │             │    ││
│  │  └─────────────┘  └─────────────┘  └─────────────┘    ││
│  └─────────────────────────────────────────────────────────┘│
│                                                             │
│  ┌─────────────────┐                                       │
│  │   PostgreSQL    │                                       │
│  │     :5432       │                                       │
│  └─────────────────┘                                       │
└─────────────────────────────────────────────────────────────┘
```

## 网络详细配置

### Litecoin 网络
- **网络类型**: Regtest (回归测试网络)
- **节点数量**: 2 个节点
- **节点连接**: 
  - `litecoin-node1` ↔ `litecoin-node2`
  - 通过环境变量 `LITE_ADDNODE` 相互发现

#### 端口映射
| 容器 | 内部端口 | 外部端口 | 用途 |
|------|----------|----------|------|
| litecoin-node1 | 19332 | 19332 | RPC |
| litecoin-node1 | 19333 | 19333 | P2P |
| litecoin-node1 | 1222 | 1222 | ZMQ |
| litecoin-node2 | 19332 | 19334 | RPC |
| litecoin-node2 | 19333 | 19335 | P2P |
| litecoin-node2 | 1222 | 1223 | ZMQ |

### Dogecoin 网络
- **网络类型**: Regtest (回归测试网络)
- **节点数量**: 2 个节点
- **节点连接**: 
  - `dogecoin-node1` ↔ `dogecoin-node2`
  - 通过环境变量 `DOGE_ADDNODE` 相互发现

#### 端口映射
| 容器 | 内部端口 | 外部端口 | 用途 |
|------|----------|----------|------|
| dogecoin-node1 | 44555 | 44555 | RPC |
| dogecoin-node1 | 44556 | 44556 | P2P |
| dogecoin-node1 | 1322 | 1322 | ZMQ |
| dogecoin-node2 | 44555 | 44557 | RPC |
| dogecoin-node2 | 44556 | 44558 | P2P |
| dogecoin-node2 | 1322 | 1323 | ZMQ |

## 网络特性

### 1. 独立性
- **Litecoin 和 Dogecoin 网络完全独立**
- 各自维护独立的区块链
- 不同的端口范围避免冲突

### 2. Regtest 模式特点
- **即时区块生成**: 可以按需生成区块
- **零难度**: 挖矿难度极低，便于测试
- **私有网络**: 与主网和测试网隔离
- **完全控制**: 可以重置、修改网络状态

### 3. 高可用性设计
- **双节点冗余**: 每个网络都有两个节点
- **自动发现**: 节点间自动连接
- **健康检查**: 每个节点都有健康检查机制

### 4. 矿池集成
- **统一接口**: 矿池可以同时连接两个网络
- **AuxPow 支持**: 支持辅助工作量证明（合并挖矿）
- **实时监控**: 通过 ZMQ 实时接收区块更新

## 网络通信流程

### 1. 节点启动流程
```
1. litecoin-node1/dogecoin-node1 启动
2. litecoin-node2/dogecoin-node2 启动
3. 节点间建立 P2P 连接
4. 同步区块链状态
5. CLI 容器连接并初始化钱包
```

### 2. 挖矿流程
```
1. 矿工连接到 mining-pool:3643
2. 矿池从 litecoin-node1 获取工作模板
3. 矿池从 dogecoin-node1 获取辅助工作
4. 矿工提交工作证明
5. 矿池验证并提交到相应网络
```

## 安全考虑

### 1. 网络隔离
- 所有服务运行在独立的 Docker 网络中
- 只有必要的端口暴露到主机

### 2. 访问控制
- RPC 接口使用用户名/密码认证
- 配置了 IP 白名单限制

### 3. 数据持久化
- 使用 Docker volumes 持久化区块链数据
- 避免数据丢失

## 总结

这是一个设计良好的测试环境，具有以下优势：
1. **完全隔离的测试网络**
2. **支持合并挖矿测试**
3. **高可用性和冗余设计**
4. **便于开发和调试**

这个网络拓扑非常适合：
- 矿池软件开发和测试
- AuxPow 功能验证
- 区块链应用开发
- 性能测试和压力测试
