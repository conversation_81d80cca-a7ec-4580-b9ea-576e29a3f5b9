docker compose -f docker-compose-local.yaml up

```shell
String to be appended to bitcoin.conf:
rpcauth=admin:7dbc86f58ef610da185f7cace2b7a0c6$ed8f536817bee628054e27f581ac53dd95450e02b490b74395bf6a680771b3e2
Your password:
qazvFGrhO5m2V1CpUyQ_wbhLDXIQ2b4HevqK8nB2VGM=
String to be appended to litecoin.conf:
rpcauth=admin:5ad49c53b67ac2a8a7d20165fd0047e8$c1d287d5c6ec52e90631aa7182cafe4b808890e275c19f08612153ddaf2cac9f
Your password:
QO5SQ7Uqv-n7VrBLh9ijf2IqNVjPmCQF6xcT2LjI79I=
String to be appended to dogecoin.conf:
rpcauth=admin:e0ccb33d35262ee4a0ed89c577c8efe$2614b296374ef501e91b073e3f7f5c6576f120c534242935b33e97ac7d542091
Your password:
XqauK7EtO6Gy_r9YvGkAEmCcV74zgG2yZVJFBkeAtkM=
Your generated algorand api key is : c1c1e41c17b023d1ff7f22eace58be5d8b6327057c3420190297a5ef200f7123
Installation finished.


curl -X POST -H "Content-type: application/json" -d '{"jsonrpc": "1.0", "id":"test", "method": "getblockchaininfo", "params":[]}' ********************************************
curl -X POST -H "Content-type: application/json" -d '{"jsonrpc": "1.0", "id":"test", "method": "getnetworkinfo", "params":[]}' ********************************************

curl -X POST -H "Content-type: application/json" -d '{
  "jsonrpc": "1.0",
  "id": "test",
  "method": "dumpwallet",
  "params": ["/opt/litecoin/dumpwallet.dat"]
}' ********************************************
rltc1q7xcw74w4puswpzetc909hxtce4a0v8en9s88d3

curl -X POST -H "Content-type: application/json" -d '{
  "jsonrpc": "1.0",
  "id": "test",
  "method": "dumpwallet",
  "params": ["/opt/dogecoin/dumpwallet.dat"]
}' ********************************************

curl -X POST -H "Content-type: application/json" -d '{
  "jsonrpc": "1.0",
  "id": "test",
  "method": "getbalance",
  "params": []
}' ********************************************

curl -X POST -H "Content-type: application/json" -d '{
  "jsonrpc": "1.0",
  "id": "test",
  "method": "listaddressgroupings",
  "params": []
}' ********************************************

curl -X POST -H "Content-type: application/json" -d '{
  "jsonrpc": "1.0",
  "id": "test",
  "method": "getblocktemplate",
  "params": [{
    "rules": ["mweb", "segwit"]
  }]
}' ********************************************


curl -X POST -H "Content-type: application/json" -d '{
  "jsonrpc": "1.0",
  "id": "curltest",
  "method": "getaddressesbylabel",
  "params": ["default"]
}' ****************************************

curl -X POST -H "Content-type: application/json" -d '{
  "jsonrpc": "1.0",
  "id": "curltest",
  "method": "getnewaddress",
  "params": []
}' ****************************************

curl -X POST -H "Content-type: application/json" -d '{
  "jsonrpc": "1.0",
  "id": "curltest",
  "method": "createwallet",
  "params": ["mywallet"]
}' ****************************************

entrypoint: >
      sh -c "
      litecoind -conf=/opt/litecoin/.litecoin/litecoin.conf -datadir=/opt/litecoin/.litecoin/db -txindex=1 -addnode=litecoin-node2 && 
      echo 'Waiting for litecoind to fully start...' && 
      while ! litecoin-cli -conf=/opt/litecoin/.litecoin/litecoin.conf getblockchaininfo; do 
        sleep 5; 
      done && 
      echo 'litecoind is ready, creating wallet...' && 
      litecoin-cli -conf=/opt/litecoin/.litecoin/litecoin.conf createwallet 'mywallet' &&
      litecoin-cli -conf=/opt/litecoin/.litecoin/litecoin.conf loadwallet 'mywallet' &&
      echo 'Wallet created, importing private key...' && 
      litecoin-cli -conf=/opt/litecoin/.litecoin/litecoin.conf importprivkey 'cPH7e9uroNuSPNNBDPARVGR5TSuNqzijVUA829RY1GbAwqTnXvsN' && 
      tail -f /dev/null
      "

curl -X POST -H "Content-type: application/json" -d '{
  "jsonrpc": "1.0",
  "id": "curltest",
  "method": "getnewaddress",
  "params": []
}' ****************************************

curl -X POST -H "Content-type: application/json" -d '{
  "jsonrpc": "1.0",
  "id": "curltest",
  "method": "getmininginfo",
  "params": []
}' ****************************************

curl -X POST -H "Content-type: application/json" -d '{
  "jsonrpc": "1.0",
  "id": "curltest",
  "method": "generatetoaddress",
  "params": [1,"rltc1qkk9rj5uww4s0yhppa35kqa0w46p4l93cml6uct"]
}' ****************************************

curl -X POST -H "Content-type: application/json" -d '{
  "jsonrpc": "1.0",
  "id": "curltest",
  "method": "generatetoaddress",
  "params": [1,"miAgQKoxXxfKppdLSHpFMoqrkQF6Aty7MD"]
}' ****************************************

{"result":"rltc1qkk9rj5uww4s0yhppa35kqa0w46p4l93cml6uct","error":null,"id":"curltest"}
{"result":"miAgQKoxXxfKppdLSHpFMoqrkQF6Aty7MD","error":null,"id":"curltest"}


curl -X POST -H "Content-type: application/json" -d '{"jsonrpc": "1.0", "id":"curltest", "method": "getnewaddress", "params":[]}' ****************************************
curl -X POST -H "Content-type: application/json" -d '{"jsonrpc": "1.0", "id":"curltest", "method": "getdifficulty", "params":[]}' ****************************************

curl -X POST -H "Content-type: application/json" -d '{"jsonrpc": "1.0", "id":"curltest", "method": "generatetoaddress", "params":[100, "rltc1q7xcw74w4puswpzetc909hxtce4a0v8en9s88d3"]}' ****************************************
curl -X POST -H "Content-type: application/json" -d '{"jsonrpc": "1.0", "id":"curltest", "method": "invalidateblock", "params":["58338ec7c9c4e6086f9eef6093d79a69d930d7ca695d1759b07180fef29d8f8e"]}' ****************************************
curl -X POST -H "Content-Type: application/json" -d '{"jsonrpc": "1.0", "id": "curltest", "method": "getnewaddress", "params":[]}' ****************************************

curl -X POST -H "Content-type: application/json" -d '{"jsonrpc": "1.0", "id":"test", "method": "generatetoaddress", "params":[10, "your_litecoin_address"]}' ****************************************



litecoind -blocknotify="curl -X POST http://127.0.0.1:8080/block-notify -d %s"

curl "http://127.0.0.1:9485/block" -d "$@"

```


```solidity
// bad-txns-vin-empty

# height should be zero but just in case
CURRENT_HEIGHT=$(litecoin-cli getblockchaininfo | jq '.blocks')
ADDR=$(litecoin-cli getnewaddress)
BLOCKS_TO_GENERATE=$((432 - $CURRENT_HEIGHT - 1))
litecoin-cli generatetoaddress $BLOCKS_TO_GENERATE $ADDR
ADDR_MWEB=$(litecoin-cli getnewaddress mweb mweb)
litecoin-cli sendtoaddress $ADDR_MWEB 1
litecoin-cli generatetoaddress 10 $ADDR

curl -X POST -H "Content-Type: application/json" -d '{"jsonrpc": "1.0", "id": "curltest", "method": "getnewaddress", "params":["mweb", "mweb"]}' ****************************************
curl -X POST -H "Content-Type: application/json" -d '{"jsonrpc": "1.0", "id": "curltest", "method": "sendtoaddress", "params":["tmweb1qqt5qe9zpfefx7p2342m2yqnluqv3jsfsxevykyw6ua0deqswr4un7ql3t996apljt5ls32udfwypfdvgvmmam4ljv4wkmlfyflhxc45zasx6yd8u", 1]}' ****************************************
curl -X POST -H "Content-type: application/json" -d '{"jsonrpc": "1.0", "id":"curltest", "method": "generatetoaddress", "params":[10, "rltc1q7xcw74w4puswpzetc909hxtce4a0v8en9s88d3"]}' ****************************************

```



