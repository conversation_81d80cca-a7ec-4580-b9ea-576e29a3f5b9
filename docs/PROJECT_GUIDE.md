# 合并挖矿池项目说明文档

## 项目概述

本项目是一个高性能的合并挖矿池软件，专注于 Dogecoin 和 Litecoin 的合并挖矿。通过先进的 Stratum 协议和 ZMQ 实时通信技术，为矿工提供高效、稳定的挖矿服务。

![Dogecoin](https://user-images.githubusercontent.com/5210627/256921635-3b7c1d9e-0148-4953-890e-5f57758973a4.png)
![Litecoin](https://user-images.githubusercontent.com/5210627/256921657-11899bf5-995b-47ce-b7af-f7ee03d4da32.png)

## 核心特性

### 🚀 技术特性
- **Stratum 网络协议**：经过 1000+ 并发客户端测试验证
- **ZMQ 实时通信**：与区块链进行实时通信订阅
- **独特 ExtraNonce 生成**：支持并行客户端工作负载
- **合并挖矿**：资源高效利用，同时挖取多种加密货币
- **API 服务**：为前端网站提供完整的 API 接口
- **RPC 故障转移**：确保高可用性
- **多种付款方案**：支持 PPLNS、PROP、SOLO 等付款模式
- **单币种挖矿**：支持单独挖矿测试

### 🔧 技术架构
- **开发语言**：Go 1.20+
- **数据库**：PostgreSQL 15+
- **容器化**：Docker 和 Docker Compose
- **消息队列**：ZMQ (ZeroMQ)
- **网络协议**：Stratum Mining Protocol

## 项目结构

```
mining-pool/
├── api/                    # API 服务层
│   ├── dashboard.go       # 仪表盘API
│   ├── hashrate.go        # 算力统计API
│   ├── miner.go           # 矿工信息API
│   ├── pool.go            # 矿池信息API
│   └── server.go          # API服务器
├── bitcoin/               # 比特币协议实现
│   ├── auxpow.go          # 辅助工作量证明
│   ├── dogecoin.go        # 狗狗币相关
│   ├── litecoin.go        # 莱特币相关
│   └── ...
├── config/                # 配置管理
├── payouts/               # 付款处理系统
│   ├── pplns.go           # PPLNS付款方案
│   ├── prop.go            # PROP付款方案
│   └── solo.go            # SOLO付款方案
├── persistence/           # 数据持久化
│   ├── schema/            # 数据库架构
│   └── ...
├── pool/                  # 矿池核心逻辑
├── rpc/                   # RPC通信管理
├── miner/                 # 矿工客户端
└── main.go                # 主程序入口
```

## 系统要求

### 硬件要求
- **CPU**: 4核心以上
- **内存**: 8GB RAM 以上
- **存储**: 50GB 可用空间
- **网络**: 稳定的网络连接

### 软件要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+)
- **Go**: 1.20 或更高版本
- **Docker**: 20.10 或更高版本
- **PostgreSQL**: 15 或更高版本

## 安装指南

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd mining-pool

# 安装 Go 依赖
go mod tidy

# 安装 Docker 和 Docker Compose
sudo apt update
sudo apt install docker.io docker-compose
```

### 2. 区块链节点配置

#### 2.1 配置 Litecoin 节点
```bash
# 编辑 Litecoin 配置文件
vim ltcdata/litecoin.conf

# 添加以下配置
server=1
rpcuser=admin
rpcpassword=admin123..1
rpcallowip=0.0.0.0/0
rpcbind=0.0.0.0
zmqpubhashblock=tcp://0.0.0.0:1222
```

#### 2.2 配置 Dogecoin 节点
```bash
# 编辑 Dogecoin 配置文件
vim dogedata/dogecoin.conf

# 添加以下配置
server=1
rpcuser=admin
rpcpassword=admin123..1
rpcallowip=0.0.0.0/0
rpcbind=0.0.0.0
zmqpubhashblock=tcp://0.0.0.0:1322
```

### 3. 数据库配置

#### 3.1 PostgreSQL 初始化
```bash
# 使用 Docker 启动 PostgreSQL
docker-compose up -d postgres

# 等待数据库启动
sleep 10

# 执行数据库初始化脚本
docker exec -it postgres psql -U root -d pool -f /docker-entrypoint-initdb.d/1-initiate.sql
docker exec -it postgres psql -U root -d pool -f /docker-entrypoint-initdb.d/2-schema.sql
```

### 4. 配置文件设置

```bash
# 复制配置示例文件
cp config.example.json config.json

# 编辑配置文件
vim config.json
```

#### 4.1 关键配置项说明

```json
{
    "pool_name": "我的挖矿池",
    "port": "3643",                    # 矿池监听端口
    "max_connections": 1000,           # 最大连接数
    "pool_difficulty": 2000,           # 矿池难度
    "merged_blockchain_order": [       # 合并挖矿顺序
        "litecoin",                    # 主链
        "dogecoin"                     # 辅助链
    ],
    "blockchains": {
        "litecoin": [{
            "name": "localhost",
            "rpc_url": "http://litecoin-node1:19332",
            "rpc_username": "admin",
            "rpc_password": "admin123..1",
            "block_notify_url": "tcp://litecoin-node1:1222",
            "reward_to": "你的莱特币地址"
        }],
        "dogecoin": [{
            "name": "localhost", 
            "rpc_url": "http://dogecoin-node1:44555",
            "rpc_username": "admin",
            "rpc_password": "admin123..1",
            "block_notify_url": "tcp://dogecoin-node1:1322",
            "reward_to": "你的狗狗币地址"
        }]
    },
    "persistence": {
        "host": "postgres",
        "port": 5432,
        "user": "root",
        "password": "password",
        "database": "pool"
    },
    "payouts": {
        "interval": "10m",             # 付款间隔
        "scheme": "PPLNS",             # 付款方案
        "chains": {
            "litecoin": {
                "reward_from": "你的莱特币地址",
                "miner_min_payment": 0.25
            },
            "dogecoin": {
                "reward_from": "你的狗狗币地址", 
                "miner_min_payment": 100000
            }
        }
    },
    "api": {
        "port": "8001"                 # API服务端口
    }
}
```

## 构建和启动

### 1. 本地构建启动

```bash
# 构建项目
./build.sh

# 启动服务
./start.sh
```

### 2. Docker 容器化启动

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f mining-pool
```

### 3. 重启服务

```bash
# 重启服务
./compose-restart.sh

# 或者使用 Docker 命令
docker-compose restart
```

## 测试指南

### 1. 单元测试

```bash
# 运行所有测试
go test ./...

# 运行特定测试
go test ./test/

# 运行矿工测试
go test ./test/miner_test.go -v
```

### 2. 集成测试

```bash
# 启动测试环境
docker-compose -f docker-compose.test.yml up -d

# 运行集成测试
go test ./tests/ -v

# 清理测试环境
docker-compose -f docker-compose.test.yml down
```

### 3. 矿工连接测试

```bash
# 构建测试矿工
./build-miner.sh

# 启动测试矿工
docker run -d --name test-miner \
  -e POOL_ADDRESS=localhost:3643 \
  -e WORKER_NAME=你的地址.worker1 \
  test-miner
```

## 使用指南

### 1. 矿工连接

矿工可以使用以下参数连接到矿池：

```
服务器地址: your-pool-domain.com:3643
用户名: 主币地址-辅助币地址.矿工ID
密码: 任意 (通常使用 'x' 或留空)
```

**示例**:
```
用户名: LTC1qxxxxx-DBxxxxx.worker1
密码: x
```

### 2. API 使用

矿池提供 RESTful API 接口：

```bash
# 获取矿池统计信息
curl http://localhost:8001/api/pool/stats

# 获取矿工信息
curl http://localhost:8001/api/miner/{address}

# 获取算力统计
curl http://localhost:8001/api/hashrate

# 获取区块信息
curl http://localhost:8001/api/blocks
```

### 3. 监控和维护

```bash
# 监控服务状态
./monitor.sh

# 查看实时日志
docker-compose logs -f mining-pool

# 检查数据库状态
docker exec -it postgres psql -U root -d pool -c "SELECT COUNT(*) FROM shares;"

# 备份数据库
docker exec postgres pg_dump -U root pool > backup.sql
```

## 配置说明

### 付款方案配置

#### PPLNS (Pay Per Last N Shares)
```json
{
    "payouts": {
        "scheme": "PPLNS",
        "interval": "10m"
    }
}
```

#### PROP (Proportional)
```json
{
    "payouts": {
        "scheme": "PROP",
        "interval": "10m"
    }
}
```

#### SOLO (Solo Mining)
```json
{
    "payouts": {
        "scheme": "SOLO",
        "interval": "10m"
    }
}
```

### 高级配置

#### 难度调整
```json
{
    "pool_difficulty": 2000,           # 初始难度
    "variable_difficulty": true,       # 启用动态难度调整
    "min_difficulty": 1000,            # 最小难度
    "max_difficulty": 10000           # 最大难度
}
```

#### 连接管理
```json
{
    "max_connections": 1000,           # 最大连接数
    "connection_timeout": "60m",       # 连接超时
    "ban_duration": "24h"             # 封禁时长
}
```

## 故障排除

### 常见问题

#### 1. 连接失败
```bash
# 检查端口是否开放
netstat -tuln | grep 3643

# 检查防火墙设置
sudo ufw status
sudo ufw allow 3643
```

#### 2. 数据库连接失败
```bash
# 检查PostgreSQL状态
docker exec postgres pg_isready -U root

# 重置数据库
docker exec postgres psql -U root -d pool -f /docker-entrypoint-initdb.d/clear-db.sql
```

#### 3. 区块链节点同步问题
```bash
# 检查节点状态
docker exec litecoin-node1 litecoin-cli -rpcuser=admin -rpcpassword=admin123..1 getblockchaininfo
docker exec dogecoin-node1 dogecoin-cli -rpcuser=admin -rpcpassword=admin123..1 getblockchaininfo
```

### 日志分析

```bash
# 查看错误日志
docker-compose logs mining-pool | grep ERROR

# 查看性能统计
docker-compose logs mining-pool | grep "STATS"

# 查看连接日志
docker-compose logs mining-pool | grep "connection"
```

## 性能优化

### 1. 数据库优化
```sql
-- 创建索引
CREATE INDEX idx_shares_performance ON shares(poolid, created, difficulty);
CREATE INDEX idx_blocks_performance ON blocks(poolid, status, created);

-- 定期清理旧数据
DELETE FROM shares WHERE created < NOW() - INTERVAL '30 days';
```

### 2. 系统优化
```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化网络参数
echo "net.core.somaxconn = 65536" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65536" >> /etc/sysctl.conf
sysctl -p
```

### 3. 监控指标
- 连接数量
- 算力统计
- 份额提交率
- 内存使用情况
- 数据库性能

## 贡献指南

### 开发环境设置
```bash
# 安装开发依赖
go mod download

# 代码格式化
gofmt -w .

# 运行代码检查
golint ./...

# 运行完整测试
go test -race ./...
```

### 提交代码
1. Fork 项目
2. 创建特性分支
3. 提交代码变更
4. 创建 Pull Request

## 许可证

本项目使用 MIT 许可证。详情请参阅 [LICENSE](LICENSE) 文件。

## 支持

如有问题或建议，请通过以下方式联系：
- [GitHub Issues](https://github.com/dreams-money/merged-mining-pool/issues)
- [GitHub Discussions](https://github.com/dreams-money/merged-mining-pool/discussions)

---

*最后更新: 2024年* 