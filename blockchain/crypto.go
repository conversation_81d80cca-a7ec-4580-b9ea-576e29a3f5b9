package blockchain

import (
	"crypto/sha256"
	"encoding/hex"

	"golang.org/x/crypto/scrypt"
)

func DoubleSha256(input string) (string, error) {
	inputBytes, err := hex.DecodeString(input)
	if err != nil {
		return "", err
	}
	sum := sha256.Sum256(inputBytes)
	sum = sha256.Sum256(sum[:])

	return hex.EncodeToString(sum[:]), nil
}

func ScryptDigest(input string) (string, error) {
	digest, err := hex.DecodeString(input)
	if err != nil {
		return "", err
	}
	digest, err = scrypt.Key(digest, digest, 1024, 1, 1, 32)
	return hex.EncodeToString(digest), nil
}

func reverseBytes(b []byte) {
	for i := len(b)/2 - 1; i >= 0; i-- {
		opp := len(b) - 1 - i
		b[i], b[opp] = b[opp], b[i]
	}
}

func DoubleSha256LE(input string) (string, error) {
	result, err := DoubleSha256(input)
	if err != nil {
		return "", err
	}
	bytes, _ := hex.DecodeString(result)
	reverseBytes(bytes)
	return hex.EncodeToString(bytes), nil
}

func ScryptDigestLE(input string) (string, error) {
	result, err := ScryptDigest(input)
	if err != nil {
		return "", err
	}
	bytes, _ := hex.DecodeString(result)
	reverseBytes(bytes)
	return hex.EncodeToString(bytes), nil
}
