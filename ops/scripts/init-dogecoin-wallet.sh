#!/bin/bash

set -e

echo "Waiting for <PERSON><PERSON><PERSON><PERSON> node to be ready..."
sleep 20

echo "Creating wallet..."
dogecoin-cli -rpcuser=admin -rpcpassword=admin123..1 -rpcport=44555 -rpcconnect=dogecoin-node1 createwallet 'mywallet' || true

echo "Loading wallet..."
dogecoin-cli -rpcuser=admin -rpcpassword=admin123..1 -rpcport=44555 -rpcconnect=dogecoin-node1 loadwallet 'mywallet' || true

echo "Importing private key..."
dogecoin-cli -rpcuser=admin -rpcpassword=admin123..1 -rpcport=44555 -rpcconnect=dogecoin-node1 importprivkey 'cUMUxKLzYn8MsKSJPexm9QLV6KZuyR3EGcuqQdWVnXtjHNxjpT1B' || true

echo "Generating initial block..."
dogecoin-cli -rpcuser=admin -rpcpassword=admin123..1 -rpcport=44555 -rpcconnect=dogecoin-node1 generatetoaddress 101 'n2rhvesmkw5ppAKAXxgNUeZp7p6SNo6KTy' || true

echo "Dogecoin wallet initialization completed!"

# Keep container running
tail -f /dev/null