#!/bin/bash

set -e

echo "Setting up Docker environment..."

# 创建目录
mkdir -p ops/ltcdata/node1 ops/ltcdata/node2 ops/ltcdata/cli
mkdir -p ops/dogedata/node1 ops/dogedata/node2 ops/dogedata/cli
mkdir -p ops/schema

# 设置权限
chmod -R 755 ops/ltcdata/ ops/dogedata/ ops/schema/ || true

# macOS 权限处理
if [[ "$OSTYPE" == "darwin"* ]]; then
    chown -R $(whoami) ops/ltcdata/ ops/dogedata/ ops/schema/ || true
fi

# 同步配置文件
if [ -f "ops/conf/litecoin.conf" ]; then
    cp ops/conf/litecoin.conf ops/ltcdata/node1/litecoin.conf
    cp ops/conf/litecoin.conf ops/ltcdata/node2/litecoin.conf
fi

if [ -f "ops/conf/dogecoin.conf" ]; then
    cp ops/conf/dogecoin.conf ops/dogedata/node1/dogecoin.conf
    cp ops/conf/dogecoin.conf ops/dogedata/node2/dogecoin.conf
fi

# 设置脚本权限
chmod +x scripts/*.sh 2>/dev/null || true

echo "✅ Setup completed"
