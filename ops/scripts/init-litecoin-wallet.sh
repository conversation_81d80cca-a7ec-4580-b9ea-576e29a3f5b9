#!/bin/bash

# Litecoin wallet initialization script
# This script sets up the initial wallet, imports keys, and generates initial blocks

set -e

echo "Waiting for Litecoin node to be ready..."
sleep 20

echo "Creating wallet..."
litecoin-cli -rpcuser=admin -rpcpassword=admin123..1 -rpcport=19332 -rpcconnect=litecoin-node1 createwallet 'mywallet' || true

echo "Loading wallet..."
litecoin-cli -rpcuser=admin -rpcpassword=admin123..1 -rpcport=19332 -rpcconnect=litecoin-node1 loadwallet 'mywallet' || true

echo "Importing private key..."
litecoin-cli -rpcuser=admin -rpcpassword=admin123..1 -rpcport=19332 -rpcconnect=litecoin-node1 importprivkey 'cMeVYECPpc9DNX68TUuqZn2guJar1trJB8eNHQ7bp2s6hD8stm7J' || true

ADDR=$(litecoin-cli -rpcuser=admin -rpcpassword=admin123..1 -rpcport=19332 -rpcconnect=litecoin-node1 getnewaddress)
echo "[INFO] 普通钱包地址: $ADDR"  || true

echo "Generating initial block..."
litecoin-cli -rpcuser=admin -rpcpassword=admin123..1 -rpcport=19332 -rpcconnect=litecoin-node1 generatetoaddress 500 "$ADDR" || true

litecoin-cli -rpcuser=admin -rpcpassword=admin123..1 -rpcport=19332 -rpcconnect=litecoin-node1 getbalance || true

echo "Litecoin wallet initialization completed!"

ADDR_MWEB=$(litecoin-cli -rpcuser=admin -rpcpassword=admin123..1 -rpcport=19332 -rpcconnect=litecoin-node1 getnewaddress mweb mweb)
echo "[INFO] MWEB钱包地址: $ADDR_MWEB" || true

echo "[STEP] 向MWEB地址转账1 LTC"
litecoin-cli -rpcuser=admin -rpcpassword=admin123..1 -rpcport=19332 -rpcconnect=litecoin-node1 sendtoaddress $ADDR_MWEB 1 || true

echo "[STEP] 再挖10块确认转账"
litecoin-cli -rpcuser=admin -rpcpassword=admin123..1 -rpcport=19332 -rpcconnect=litecoin-node1 generatetoaddress 10 "$ADDR" || true

echo "[SUCCESS] MWEB激活并转账完成"
# Keep container running
tail -f /dev/null