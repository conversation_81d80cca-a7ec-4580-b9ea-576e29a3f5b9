services:
  postgres:
    image: postgres:15
    container_name: postgres
    # user: "1000:1000"  # Commented out to avoid permission issues
    environment:
      POSTGRES_USER: root
      POSTGRES_PASSWORD: password
      POSTGRES_DB: pool
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    networks:
      - network-mining-pool
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
      - ./schema:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U root -d pool"]
      interval: 10s
      timeout: 5s
      retries: 5
  
networks:
  network-mining-pool:
    driver: bridge
      
# volumes:
  # postgres_data:
  #   driver: local
  