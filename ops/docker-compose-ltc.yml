services:
 
  litecoin-node1:
    image: coinflow/ltc-node:0.21.4
    container_name: litecoin-node1
    restart: on-failure:3
    platform: linux/amd64  # Specify platform for Apple Silicon compatibility
    environment:
      - TZ=Europe/London
      - LITE_ADDNODE=litecoin-node2
    ports:
      - "19332:19332"
      - "19333:19333"
      - "1222:1222"
    networks:
      - network-mining-pool
    volumes:
      - ${SHARED_WORKSPACE:-.}/ops/ltcdata/node1:/opt/litecoin/.litecoin
    healthcheck:
      test: [ "CMD-SHELL", "litecoin-cli -rpcuser=admin -rpcpassword=admin123..1 -rpcport=19332 getblockchaininfo" ]
      interval: 10s
      timeout: 5s
      retries: 5
  

networks:
  network-mining-pool:
    driver: bridge
      
volumes:
  litecoin_node1_data:
    driver: local
  