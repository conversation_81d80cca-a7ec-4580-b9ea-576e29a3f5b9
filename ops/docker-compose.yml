services:
  postgres:
    image: postgres:15
    container_name: postgres
    # user: "1000:1000"  # Commented out to avoid permission issues
    environment:
      POSTGRES_USER: root
      POSTGRES_PASSWORD: password
      POSTGRES_DB: pool
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    networks:
      - network-mining-pool
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ${SHARED_WORKSPACE:-..}/ops/schema:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U root -d pool"]
      interval: 10s
      timeout: 5s
      retries: 5
  litecoin-node1:
    image: coinflow/ltc-node:0.21.4
    container_name: litecoin-node1
    restart: on-failure:3
    platform: linux/amd64  # Specify platform for Apple Silicon compatibility
    environment:
      - TZ=Europe/London
      - LITE_ADDNODE=litecoin-node2
    ports:
      - "19332:19332"
      - "19333:19333"
      - "1222:1222"
    networks:
      - network-mining-pool
    volumes:
      - ${SHARED_WORKSPACE:-..}/ops/ltcdata/node1:/opt/litecoin/.litecoin
    healthcheck:
      test: [ "CMD-SHELL", "litecoin-cli -rpcuser=admin -rpcpassword=admin123..1 -rpcport=19332 getblockchaininfo" ]
      interval: 10s
      timeout: 5s
      retries: 5
  litecoin-node2:
    image: coinflow/ltc-node:0.21.4
    container_name: litecoin-node2
    restart: on-failure:3
    platform: linux/amd64  # Specify platform for Apple Silicon compatibility
    environment:
      - TZ=Europe/London
      - LITE_ADDNODE=litecoin-node1
    ports:
      - "19334:19332"
      - "19335:19333"
      - "1223:1222"
    networks:
      - network-mining-pool
    volumes:
      - ${SHARED_WORKSPACE:-..}/ops/ltcdata/node2:/opt/litecoin/.litecoin
    healthcheck:
      test: [ "CMD-SHELL", "litecoin-cli -rpcuser=admin -rpcpassword=admin123..1 -rpcport=19332 getblockchaininfo" ]
      interval: 10s
      timeout: 5s
      retries: 5
  litecoin-cli:
    image: coinflow/ltc-node:0.21.4
    container_name: litecoin-cli
    platform: linux/amd64  # Specify platform for Apple Silicon compatibility
    networks:
      - network-mining-pool
    volumes:
      - ${SHARED_WORKSPACE:-..}/ops/scripts/init-litecoin-wallet.sh:/scripts/init-litecoin-wallet.sh
      - ${SHARED_WORKSPACE:-..}/ops/ltcdata/cli:/opt/litecoin/.litecoin  # CLI 专用目录，避免启动节点
    environment:
      - TZ=Europe/London
    depends_on:
      litecoin-node1:
        condition: service_healthy
    # 创建一个最小化的配置，只用于 CLI 操作，不启动节点
    entrypoint:
      - /bin/bash
      - -c
      - |
        # 创建最小化配置，只用于 CLI 连接
        mkdir -p /opt/litecoin/.litecoin
        echo "regtest=1" > /opt/litecoin/.litecoin/litecoin.conf
        echo "rpcuser=admin" >> /opt/litecoin/.litecoin/litecoin.conf
        echo "rpcpassword=admin123..1" >> /opt/litecoin/.litecoin/litecoin.conf
        echo "rpcconnect=litecoin-node1" >> /opt/litecoin/.litecoin/litecoin.conf
        echo "rpcport=19332" >> /opt/litecoin/.litecoin/litecoin.conf
        # 运行钱包初始化脚本
        /scripts/init-litecoin-wallet.sh
  dogecoin-node1:
    image: coinflow/doge-node:1.14.9
    container_name: dogecoin-node1
    restart: on-failure:3
    platform: linux/amd64  # Specify platform for Apple Silicon compatibility
    environment:
      - TZ=Europe/London
      - DOGE_ADDNODE=dogecoin-node2
    ports:
      - "44555:44555"
      - "44556:44556"
      - "1322:1322"
    networks:
      - network-mining-pool
    volumes:
      - ${SHARED_WORKSPACE:-..}/ops/dogedata/node1:/opt/dogecoin/.dogecoin
    healthcheck:
      test: [ "CMD-SHELL", "dogecoin-cli -rpcuser=admin -rpcpassword=admin123..1 -rpcport=44555 getblockchaininfo" ]
      interval: 10s
      timeout: 5s
      retries: 5
  dogecoin-node2:
    image: coinflow/doge-node:1.14.9
    container_name: dogecoin-node2
    restart: on-failure:3
    platform: linux/amd64  # Specify platform for Apple Silicon compatibility
    environment:
      - TZ=Europe/London
      - DOGE_ADDNODE=dogecoin-node1
    ports:
      - "44557:44555"
      - "44558:44556"
      - "1323:1322"
    networks:
      - network-mining-pool
    volumes:
      - ${SHARED_WORKSPACE:-..}/ops/dogedata/node2:/opt/dogecoin/.dogecoin
    healthcheck:
      test: [ "CMD-SHELL", "dogecoin-cli -rpcuser=admin -rpcpassword=admin123..1 -rpcport=44555 getblockchaininfo" ]
      interval: 10s
      timeout: 5s
      retries: 5
  dogecoin-cli:
    image: coinflow/doge-node:1.14.9
    container_name: dogecoin-cli
    platform: linux/amd64  # Specify platform for Apple Silicon compatibility
    networks:
      - network-mining-pool
    volumes:
      - ${SHARED_WORKSPACE:-..}/ops/scripts/init-dogecoin-wallet.sh:/scripts/init-dogecoin-wallet.sh
      - ${SHARED_WORKSPACE:-..}/ops/dogedata/cli:/opt/dogecoin/.dogecoin  # CLI 专用目录，避免启动节点
    environment:
      - TZ=Europe/London
    depends_on:
      dogecoin-node1:
        condition: service_healthy
    # 创建一个最小化的配置，只用于 CLI 操作，不启动节点
    entrypoint:
      - /bin/bash
      - -c
      - |
        # 创建最小化配置，只用于 CLI 连接
        mkdir -p /opt/dogecoin/.dogecoin
        echo "regtest=1" > /opt/dogecoin/.dogecoin/dogecoin.conf
        echo "rpcuser=admin" >> /opt/dogecoin/.dogecoin/dogecoin.conf
        echo "rpcpassword=admin123..1" >> /opt/dogecoin/.dogecoin/dogecoin.conf
        echo "rpcconnect=dogecoin-node1" >> /opt/dogecoin/.dogecoin/dogecoin.conf
        echo "rpcport=44555" >> /opt/dogecoin/.dogecoin/dogecoin.conf
        # 运行钱包初始化脚本
        /scripts/init-dogecoin-wallet.sh
  mining-pool:
    image: coinflow/mining-pool:latest
    container_name: mining-pool
    restart: unless-stopped
    platform: linux/amd64  # Specify platform for Apple Silicon compatibility
    ports:
      - "3643:3643"
      - "8001:8001"
    volumes:
      - ${SHARED_WORKSPACE:-..}/ops/config.json:/root/config.json
    networks:
      - network-mining-pool
    depends_on:
      - postgres
      - dogecoin-cli
      - litecoin-cli
      - litecoin-node1
      - dogecoin-node1
    healthcheck:
      test: [ "CMD", "nc", "-z", "localhost", "3643" ]
      interval: 10s
      timeout: 5s
      retries: 5
  miner1:
    build:
      dockerfile: Dockerfile-miner
    container_name: miner1
    environment:
      - POOL_ADDRESS=mining-pool:3643
      - WORKER_NAME=primarycoinAddress-auxcoinAddress.worker1
    restart: unless-stopped
    networks:
      - network-mining-pool
    depends_on:
      - mining-pool
  miner2:
    build:
      dockerfile: Dockerfile-miner
    container_name: miner2
    environment:
      - POOL_ADDRESS=mining-pool:3643
      - WORKER_NAME=primarycoinAddress-auxcoinAddress.worker2
    restart: unless-stopped
    networks:
      - network-mining-pool
    depends_on:
      - mining-pool

networks:
  network-mining-pool:
    driver: bridge
      
volumes:
  postgres_data:
    driver: local
  litecoin_node1_data:
    driver: local
  litecoin_node2_data:
    driver: local
  dogecoin_node1_data:
    driver: local
  dogecoin_node2_data:
    driver: local