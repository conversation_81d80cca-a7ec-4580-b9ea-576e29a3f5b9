合并挖矿池（Merged Mining Pool）
============================

高性能、易部署的合并挖矿池软件，专注于 Dogecoin 和 Litecoin 的合并挖矿，支持高并发、自动化运维、Docker 一键部署，适合开发、测试和生产环境。

![Dog<PERSON><PERSON><PERSON>](https://user-images.githubusercontent.com/5210627/256921635-3b7c1d9e-0148-4953-890e-5f57758973a4.png)
![Litecoin Logo](https://user-images.githubusercontent.com/5210627/256921657-11899bf5-995b-47ce-b7af-f7ee03d4da32.png)

项目特性
--------
- 支持 Dogecoin/Litecoin 合并挖矿（AuxPoW）
- Stratum 协议，单池支持 1000+ 并发矿工
- ZMQ 区块链事件实时推送
- 多种奖励方案：PPLNS、PROP、SOLO
- API 服务，便于前端/监控集成
- 健康检查与高可用设计
- Docker Compose 一键启动，支持 ARM/AMD 平台
- CI/CD 自动化测试与构建（Woodpecker）
- 完善的权限与平台兼容性处理

快速开始
--------

### 1. 环境依赖
- Go 1.22+
- Docker & Docker Compose
- PostgreSQL 15+

### 2. 配置区块链节点
- 编辑 `ltcdata/litecoin.conf` 和 `dogedata/dogecoin.conf`，参考内置模板，确保 RPC/ZMQ 端口与主配置一致。

### 3. 配置数据库
- 默认使用 Docker 启动 Postgres，无需手动初始化。

### 4. 配置矿池参数
- 复制 `config.example.json` 为 `config.json`，根据实际钱包地址、端口等修改。

### 5. 一键启动
```bash
# 启动所有服务（推荐）
docker-compose up -d
# 查看服务状态
docker-compose ps
# 查看日志
docker-compose logs -f mining-pool
```

典型用法
--------

### 矿工连接
- 服务器地址：`your-pool-domain.com:3643`
- 用户名：`主币地址-辅币地址.矿工ID`  （如：LTC1qxxx-DBxxx.worker1）
- 密码：任意（通常用 x 或留空）

### API 示例
```bash
curl http://localhost:8001/api/pool/stats
curl http://localhost:8001/api/miner/{address}
curl http://localhost:8001/api/hashrate
```

### 监控与维护
```bash
./monitor.sh
# 或 docker-compose logs -f mining-pool
```

测试与 CI/CD
------------
- 单元测试：`go test ./...`
- 集成测试：`docker-compose -f docker-compose.test.yml up -d && go test ./tests/`
- Woodpecker CI 已集成自动测试与多阶段 Docker 构建，详见 `.woodpecker/` 目录。

常见问题与排查
--------------
- 端口未监听/连接失败：确认服务已启动，端口未被占用，防火墙已放行。
- 区块链节点同步慢/区块错误：建议清理数据目录，升级节点版本，或调整 keypool。
- Docker 权限/挂载问题：推荐全部使用 named volumes，避免 bind mount。
- ARM Mac 平台：已内置 `platform: linux/amd64`，无需额外设置。

贡献与支持
----------
- 欢迎提交 PR、Issue 或在 [GitHub Discussions](https://github.com/dreams-money/merged-mining-pool/discussions) 交流。
- 详细开发规范见 `.cursor/rules`。
- 许可证：MIT

---
最后更新：2024年
