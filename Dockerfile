# syntax=docker/dockerfile:1.4
# 使用多阶段构建自动编译 dogepool

# ---------- 构建阶段 ----------
FROM golang:1.23.0 AS builder
WORKDIR /app

# 缓存依赖
COPY go.mod go.sum ./
RUN --mount=type=cache,target=/go/pkg/mod \
    go mod download

# 复制源码并编译
COPY . .
RUN --mount=type=cache,target=/root/.cache/go-build \
    --mount=type=cache,target=/go/pkg/mod \
    CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o dogepool .

# ---------- 运行阶段 ----------
FROM alpine:latest
WORKDIR /root/

# 复制编译后的可执行文件
COPY --from=builder /app/dogepool ./dogepool

# 复制默认配置（如果需要）
COPY config.json ./config.json

# 增加执行权限
RUN chmod +x ./dogepool

EXPOSE 3643

CMD ["./dogepool", "config.json"]
