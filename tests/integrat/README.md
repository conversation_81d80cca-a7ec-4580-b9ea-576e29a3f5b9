# 集成测试优化说明

## 概述

本目录包含优化后的集成测试，专注于测试矿池服务的核心功能和连接性。

## 文件结构

```
tests/integrat/
├── README.md              # 本文档
├── main_test.go           # 测试主入口和服务检查
├── testutils.go           # 公共测试工具和配置
├── integration_test.go    # 基础集成测试
└── miner_test.go          # 矿工连接和挖矿测试
```

## 核心特性

### 1. 服务状态检查
- **主要检查**: 矿池 Stratum 端口 (3643)
- **可选检查**: Web API 端口 (8001)
- **依赖检查**: PostgreSQL, Litecoin RPC, Dogecoin RPC

### 2. 符合 Go 测试生命周期
- 使用 `TestMain` 进行测试前置设置
- 自动等待服务就绪
- 统一的错误处理和日志记录

### 3. 公共测试工具
- `ServiceChecker`: 服务状态检查器
- `TestConfig`: 测试配置管理
- 重试机制和超时控制
- 统一的日志格式

## 测试类型

### 基础连接测试 (`integration_test.go`)
- 矿池服务连接性测试
- 依赖服务可用性测试
- API 端点健康检查

### 矿工功能测试 (`miner_test.go`)
- 矿工连接到矿池
- Stratum 协议交互
- 短时间挖矿测试

## 配置

### 默认端口配置
```go
StratumPort:     "3643"  // 矿池 Stratum 端口
WebAPIPort:      "8001"  // Web API 端口 (可选)
PostgreSQLPort:  "5432"  // 数据库端口
LitecoinRPCPort: "19332" // Litecoin RPC 端口
DogecoinRPCPort: "44555" // Dogecoin RPC 端口
```

### 超时配置
```go
DefaultTimeout:      3 * time.Minute  // 服务等待超时
ServicePollInterval: 5 * time.Second  // 服务检查间隔
```

## 运行测试

### 本地运行
```bash
# 启动服务
make docker-up

# 运行集成测试
go test -v ./tests/integrat/

# 运行特定测试
go test -v ./tests/integrat/ -run TestMiningPoolConnectivity
```

### CI 环境
测试会自动在 Woodpecker CI 中运行：
```yaml
# .woodpecker/test.yml
- go test -v ./tests/integrat/*.go
```

## 测试流程

### 1. 测试前置 (TestMain)
```
1. 验证测试环境配置
2. 等待依赖服务就绪 (PostgreSQL, 区块链节点)
3. 等待矿池服务就绪 (端口 3643)
4. 额外等待确保服务完全初始化
5. 开始运行测试
```

### 2. 测试执行
```
1. 基础连接测试
   ├── 矿池 Stratum 端口测试
   ├── Web API 端点测试 (可选)
   └── 依赖服务连接测试

2. 矿工功能测试
   ├── 矿工连接测试
   ├── Stratum 协议测试
   └── 短时间挖矿测试
```

### 3. 测试清理
- 自动关闭连接
- 清理测试资源
- 记录测试结果

## 优势

### 1. 可靠性
- ✅ 自动等待服务就绪
- ✅ 重试机制和超时控制
- ✅ 详细的错误日志

### 2. 效率
- ✅ 只检查必要的服务 (3643 端口)
- ✅ 并行测试支持
- ✅ 快速失败机制

### 3. 可维护性
- ✅ 公共工具函数
- ✅ 统一的配置管理
- ✅ 清晰的测试结构

### 4. 灵活性
- ✅ 可配置的超时和重试
- ✅ 可选服务检查
- ✅ 环境适配

## 故障排除

### 常见问题

1. **服务未就绪**
   ```
   Error: mining pool services did not become ready within 3m0s timeout
   ```
   - 检查 Docker 服务是否启动
   - 检查端口 3643 是否被占用
   - 增加超时时间

2. **连接被拒绝**
   ```
   Error: connection refused
   ```
   - 确认矿池服务正在运行
   - 检查防火墙设置
   - 验证网络配置

3. **测试超时**
   ```
   Error: test timeout
   ```
   - 检查系统资源使用情况
   - 调整测试超时配置
   - 查看服务日志

### 调试技巧

1. **查看服务状态**
   ```bash
   make docker-status
   ```

2. **查看服务日志**
   ```bash
   make docker-logs
   ```

3. **手动测试连接**
   ```bash
   telnet localhost 3643
   ```

## 扩展

如需添加新的测试：

1. 在相应的测试文件中添加测试函数
2. 使用公共的 `testConfig` 和 `serviceChecker`
3. 遵循现有的日志和错误处理模式
4. 确保测试具有适当的超时和清理机制
