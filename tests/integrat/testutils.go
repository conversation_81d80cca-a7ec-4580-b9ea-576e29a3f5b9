package integrat

import (
	"fmt"
	"log"
	"net"
	"net/http"
	"time"
)

// TestConfig 测试配置
type TestConfig struct {
	MiningPoolHost     string
	StratumPort        string
	WebAPIPort         string
	PostgreSQLPort     string
	LitecoinRPCPort    string
	DogecoinRPCPort    string
	DefaultTimeout     time.Duration
	ServicePollInterval time.Duration
}

// DefaultTestConfig 返回默认测试配置
func DefaultTestConfig() *TestConfig {
	return &TestConfig{
		MiningPoolHost:      "localhost",
		StratumPort:         "3643",
		WebAPIPort:          "8001",
		PostgreSQLPort:      "5432",
		LitecoinRPCPort:     "19332",
		DogecoinRPCPort:     "44555",
		DefaultTimeout:      3 * time.Minute,
		ServicePollInterval: 5 * time.Second,
	}
}

// WaitForService 等待指定服务可用
func (tc *TestConfig) WaitForService(serviceName, port string) error {
	log.Printf("Waiting for %s on port %s...", serviceName, port)
	
	deadline := time.Now().Add(tc.DefaultTimeout)
	for {
		if tc.IsPortOpen(port) {
			log.Printf("✓ %s is ready on port %s", serviceName, port)
			return nil
		}
		
		if time.Now().After(deadline) {
			return fmt.Errorf("%s did not become ready within %v timeout", serviceName, tc.DefaultTimeout)
		}
		
		time.Sleep(tc.ServicePollInterval)
	}
}

// IsPortOpen 检查端口是否开放
func (tc *TestConfig) IsPortOpen(port string) bool {
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%s", tc.MiningPoolHost, port), 3*time.Second)
	if err != nil {
		return false
	}
	conn.Close()
	return true
}

// CheckHTTPEndpoint 检查 HTTP 端点
func (tc *TestConfig) CheckHTTPEndpoint(url string) (bool, int) {
	client := &http.Client{
		Timeout: 5 * time.Second,
	}
	
	resp, err := client.Get(url)
	if err != nil {
		return false, 0
	}
	defer resp.Body.Close()
	
	return resp.StatusCode < 500, resp.StatusCode
}

// GetMiningPoolAddress 获取矿池地址
func (tc *TestConfig) GetMiningPoolAddress() string {
	return fmt.Sprintf("%s:%s", tc.MiningPoolHost, tc.StratumPort)
}

// GetWebAPIURL 获取 Web API URL
func (tc *TestConfig) GetWebAPIURL(path string) string {
	return fmt.Sprintf("http://%s:%s%s", tc.MiningPoolHost, tc.WebAPIPort, path)
}

// LogTestStart 记录测试开始
func LogTestStart(testName string) {
	log.Printf("=== Starting %s ===", testName)
}

// LogTestEnd 记录测试结束
func LogTestEnd(testName string, success bool) {
	status := "PASSED"
	if !success {
		status = "FAILED"
	}
	log.Printf("=== %s %s ===", testName, status)
}

// LogServiceCheck 记录服务检查
func LogServiceCheck(serviceName string, available bool) {
	status := "✓"
	if !available {
		status = "✗"
	}
	log.Printf("%s %s service check", status, serviceName)
}

// RetryOperation 重试操作
func RetryOperation(operation func() error, maxRetries int, delay time.Duration) error {
	var lastErr error
	
	for i := 0; i < maxRetries; i++ {
		if err := operation(); err != nil {
			lastErr = err
			if i < maxRetries-1 {
				log.Printf("Operation failed (attempt %d/%d): %v, retrying in %v...", i+1, maxRetries, err, delay)
				time.Sleep(delay)
				continue
			}
		} else {
			return nil
		}
	}
	
	return fmt.Errorf("operation failed after %d attempts, last error: %v", maxRetries, lastErr)
}

// MeasureExecutionTime 测量执行时间
func MeasureExecutionTime(operation func() error) (time.Duration, error) {
	start := time.Now()
	err := operation()
	duration := time.Since(start)
	return duration, err
}

// ValidateTestEnvironment 验证测试环境
func ValidateTestEnvironment(config *TestConfig) error {
	requiredServices := []struct {
		name string
		port string
	}{
		{"PostgreSQL", config.PostgreSQLPort},
		{"Litecoin RPC", config.LitecoinRPCPort},
		{"Dogecoin RPC", config.DogecoinRPCPort},
		{"Mining Pool Stratum", config.StratumPort},
	}
	
	log.Println("Validating test environment...")
	
	for _, service := range requiredServices {
		if !config.IsPortOpen(service.port) {
			return fmt.Errorf("required service %s is not available on port %s", service.name, service.port)
		}
		LogServiceCheck(service.name, true)
	}
	
	// 检查可选服务
	if config.IsPortOpen(config.WebAPIPort) {
		LogServiceCheck("Mining Pool Web API", true)
	} else {
		log.Printf("ℹ Mining Pool Web API is not available (optional)")
	}
	
	log.Println("✓ Test environment validation completed")
	return nil
}
