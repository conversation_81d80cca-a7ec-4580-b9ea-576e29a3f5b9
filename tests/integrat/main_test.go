package integrat

import (
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"testing"
	"time"
)

// ServiceChecker 提供服务状态检查的公共功能
type ServiceChecker struct {
	timeout      time.Duration
	pollInterval time.Duration
}

// NewServiceChecker 创建新的服务检查器
func NewServiceChecker() *ServiceChecker {
	return &ServiceChecker{
		timeout:      3 * time.Minute,
		pollInterval: 5 * time.Second,
	}
}

// WaitForMiningPool 等待矿池服务就绪（检查 3643 和 8001 端口）
func (sc *ServiceChecker) WaitForMiningPool() error {
	log.Println("Waiting for mining pool services to be ready...")

	deadline := time.Now().Add(sc.timeout)
	for {
		if sc.checkMiningPoolReady() {
			log.Println("✓ Mining pool services are ready")
			return nil
		}

		if time.Now().After(deadline) {
			return fmt.Errorf("mining pool services did not become ready within %v timeout", sc.timeout)
		}

		log.Printf("Mining pool not ready yet. Retrying in %v...", sc.pollInterval)
		time.Sleep(sc.pollInterval)
	}
}

// checkMiningPoolReady 检查矿池服务是否就绪
func (sc *ServiceChecker) checkMiningPoolReady() bool {
	// 检查 Stratum 端口 (3643)
	if !sc.isPortOpen("localhost", "3643") {
		log.Printf("Stratum port 3643 is not open")
		return false
	}

	// 检查 Web API 端口 (8001) - 可选
	if sc.isPortOpen("localhost", "8001") {
		log.Printf("✓ Web API port 8001 is also available")
	}

	return true
}

// WaitForDependencies 等待依赖服务就绪
func (sc *ServiceChecker) WaitForDependencies() error {
	log.Println("Waiting for dependency services to be ready...")

	requiredServices := []struct {
		name string
		port string
	}{
		{"PostgreSQL", "5432"},
		{"Litecoin Node", "19332"},
		{"Dogecoin Node", "44555"},
	}

	deadline := time.Now().Add(sc.timeout)
	for {
		allReady := true
		for _, service := range requiredServices {
			if !sc.isPortOpen("localhost", service.port) {
				log.Printf("%s (port %s) is not ready", service.name, service.port)
				allReady = false
				break
			}
		}

		if allReady {
			log.Println("✓ All dependency services are ready")
			return nil
		}

		if time.Now().After(deadline) {
			return fmt.Errorf("dependency services did not become ready within %v timeout", sc.timeout)
		}

		log.Printf("Dependencies not ready yet. Retrying in %v...", sc.pollInterval)
		time.Sleep(sc.pollInterval)
	}
}

// isPortOpen 检查端口是否开放
func (sc *ServiceChecker) isPortOpen(host, port string) bool {
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%s", host, port), 3*time.Second)
	if err != nil {
		return false
	}
	conn.Close()
	return true
}

// CheckHTTPEndpoint 检查 HTTP 端点是否可访问
func (sc *ServiceChecker) CheckHTTPEndpoint(url string) bool {
	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	resp, err := client.Get(url)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	return resp.StatusCode < 500 // 接受所有非服务器错误状态码
}

// 全局服务检查器和配置实例
var (
	serviceChecker = NewServiceChecker()
	testConfig     = DefaultTestConfig()
)

func TestMain(m *testing.M) {
	log.Println("=== Integration Test Setup ===")

	// 1. 验证测试环境
	if err := ValidateTestEnvironment(testConfig); err != nil {
		log.Fatalf("Test environment validation failed: %v", err)
	}

	// 2. 等待依赖服务就绪
	if err := serviceChecker.WaitForDependencies(); err != nil {
		log.Fatalf("Failed to wait for dependencies: %v", err)
	}

	// 3. 等待矿池服务就绪
	if err := serviceChecker.WaitForMiningPool(); err != nil {
		log.Fatalf("Failed to wait for mining pool: %v", err)
	}

	// 4. 额外等待时间确保服务完全初始化
	log.Println("Services ready, waiting for full initialization...")
	time.Sleep(10 * time.Second)

	log.Println("=== Starting Integration Tests ===")

	// 运行测试
	code := m.Run()

	log.Println("=== Integration Tests Completed ===")

	// 退出测试
	os.Exit(code)
}
