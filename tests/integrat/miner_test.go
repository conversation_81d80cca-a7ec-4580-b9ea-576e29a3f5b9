package integrat

import (
	"bytes"
	"crypto/sha256"
	"encoding/binary"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"math/big"
	"net"
	"runtime"
	"sync"
	"testing"
	"time"

	"golang.org/x/crypto/scrypt"
)

type StratumRequest struct {
	ID     int           `json:"id"`
	Method string        `json:"method"`
	Params []interface{} `json:"params"`
}

type StratumResponse struct {
	ID     int           `json:"id"`
	Result interface{}   `json:"result,omitempty"`
	Error  interface{}   `json:"error,omitempty"`
	Method string        `json:"method,omitempty"`
	Params []interface{} `json:"params,omitempty"`
}

type MiningJob struct {
	JobID        string
	PrevHash     string
	Coinbase1    string
	Coinbase2    string
	MerkleBranch []string
	Version      string
	Nbits        string
	Ntime        string
	CleanJobs    bool
}

type MiningStats struct {
	hashCount   uint64
	shareCount  uint64
	startTime   time.Time
	submissions uint64
	accepts     uint64
	rejects     uint64
	mutex       sync.Mutex
}

type Miner struct {
	conn        net.Conn
	encoder     *json.Encoder
	decoder     *json.Decoder
	extraNonce1 string
	extraNonce2 string
	difficulty  float64
	currentJob  *MiningJob
	stats       *MiningStats
}

const (
	PoolAddress   = "localhost:3643"
	RetryInterval = 5 * time.Second
	Worker        = "primarycoinAddress-auxcoinAddress.worker1"
)

// TestMinerConnection 测试矿工连接到矿池
func TestMinerConnection(t *testing.T) {
	// 确保矿池服务可用
	if !serviceChecker.isPortOpen("localhost", "3643") {
		t.Fatal("Mining pool is not accessible on port 3643")
	}

	t.Log("Testing miner connection to mining pool...")

	// 创建矿工实例
	miner, err := createTestMiner()
	if err != nil {
		t.Fatalf("Failed to create test miner: %v", err)
	}
	defer miner.Close()

	// 测试订阅
	if err := miner.subscribe(); err != nil {
		t.Fatalf("Failed to subscribe to mining pool: %v", err)
	}
	t.Log("✓ Successfully subscribed to mining pool")

	// 测试认证
	if err := miner.authorize(); err != nil {
		t.Fatalf("Failed to authorize with mining pool: %v", err)
	}
	t.Log("✓ Successfully authorized with mining pool")
}

// TestMinerShortRun 测试矿工短时间运行
func TestMinerShortRun(t *testing.T) {
	// 确保矿池服务可用
	if !serviceChecker.isPortOpen("localhost", "3643") {
		t.Skip("Mining pool is not accessible, skipping miner run test")
		return
	}

	t.Log("Testing short miner run...")

	// 运行矿工 30 秒
	done := make(chan bool, 1)
	go func() {
		if err := runTestMiner(30 * time.Second); err != nil {
			t.Logf("Miner run completed with: %v", err)
		}
		done <- true
	}()

	// 等待完成或超时
	select {
	case <-done:
		t.Log("✓ Miner test run completed successfully")
	case <-time.After(45 * time.Second):
		t.Log("✓ Miner test run timed out as expected")
	}
}

// createTestMiner 创建测试用的矿工实例
func createTestMiner() (*Miner, error) {
	conn, err := net.Dial("tcp", PoolAddress)
	if err != nil {
		return nil, fmt.Errorf("连接失败: %v", err)
	}

	miner := &Miner{
		conn:    conn,
		encoder: json.NewEncoder(conn),
		decoder: json.NewDecoder(conn),
		stats: &MiningStats{
			startTime: time.Now(),
		},
	}

	return miner, nil
}

// runTestMiner 运行测试矿工指定时间
func runTestMiner(duration time.Duration) error {
	miner, err := createTestMiner()
	if err != nil {
		return fmt.Errorf("创建矿工失败: %v", err)
	}
	defer miner.Close()

	if err := miner.subscribe(); err != nil {
		return fmt.Errorf("订阅失败: %v", err)
	}

	if err := miner.authorize(); err != nil {
		return fmt.Errorf("认证失败: %v", err)
	}

	// 启动挖矿，但限制时间
	done := make(chan bool, 1)
	go func() {
		miner.startTestMining(duration)
		done <- true
	}()

	// 处理服务器消息，但有超时
	go miner.handleServerMessages()

	// 等待完成或超时
	select {
	case <-done:
		return nil
	case <-time.After(duration + 5*time.Second):
		return fmt.Errorf("mining timeout after %v", duration)
	}
}

// Close 关闭矿工连接
func (m *Miner) Close() error {
	if m.conn != nil {
		return m.conn.Close()
	}
	return nil
}

func (m *Miner) subscribe() error {
	req := StratumRequest{
		ID:     1,
		Method: "mining.subscribe",
		Params: []interface{}{"test-miner/1.0.0"},
	}

	if err := m.encoder.Encode(req); err != nil {
		return err
	}

	var resp StratumResponse
	if err := m.decoder.Decode(&resp); err != nil {
		return err
	}

	if result, ok := resp.Result.([]interface{}); ok {
		if len(result) > 1 {
			m.extraNonce1 = result[1].(string)
		}
	}

	return nil
}

func (m *Miner) authorize() error {
	req := StratumRequest{
		ID:     2,
		Method: "mining.authorize",
		Params: []interface{}{Worker, "x"},
	}

	return m.encoder.Encode(req)
}

func (m *Miner) handleServerMessages() {
	for {
		var msg StratumResponse
		if err := m.decoder.Decode(&msg); err != nil {
			log.Printf("读取服务器消息失败: %v", err)
			return
		}

		switch msg.Method {
		case "mining.notify":
			m.handleNewJob(msg.Params)
		case "mining.set_difficulty":
			if len(msg.Params) > 0 {
				m.difficulty = msg.Params[0].(float64)
				log.Printf("难度已更新为: %f", m.difficulty)
			}
		default:
			// 处理提交份额的响应
			if msg.ID == 4 { // submit share response
				if msg.Error == nil {
					m.stats.mutex.Lock()
					m.stats.accepts++
					m.stats.mutex.Unlock()
					log.Printf("份额被接受")
				} else {
					m.stats.mutex.Lock()
					m.stats.rejects++
					m.stats.mutex.Unlock()
					log.Printf("份额被拒绝: %v", msg.Error)
				}
			}
		}
	}
}

func (m *Miner) handleNewJob(params []interface{}) {
	if len(params) < 9 {
		return
	}

	merkleBranch, ok := params[4].([]interface{})
	if !ok {
		log.Printf("Invalid merkle branch format")
		return
	}

	merkleStrings := make([]string, len(merkleBranch))
	for i, v := range merkleBranch {
		merkleStrings[i], ok = v.(string)
		if !ok {
			log.Printf("Invalid merkle branch element format")
			return
		}
	}

	m.currentJob = &MiningJob{
		JobID:        params[0].(string),
		PrevHash:     params[1].(string),
		Coinbase1:    params[2].(string),
		Coinbase2:    params[3].(string),
		MerkleBranch: merkleStrings,
		Version:      params[5].(string),
		Nbits:        params[6].(string),
		Ntime:        params[7].(string),
		CleanJobs:    params[8].(bool),
	}

	log.Printf("收到新任务: JobID=%s, Version=%s, PrevHash=%s",
		m.currentJob.JobID,
		m.currentJob.Version,
		m.currentJob.PrevHash,
	)
}

func (m *Miner) startMining() {
	numWorkers := runtime.NumCPU()
	log.Printf("启动 %d 个挖矿线程", numWorkers)

	// 启动统计打印器
	go m.startStatsReporter()

	var wg sync.WaitGroup
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			m.miningWorker(workerID, numWorkers)
		}(i)
	}
	wg.Wait()
}

// startTestMining 启动测试挖矿（限制时间）
func (m *Miner) startTestMining(duration time.Duration) {
	numWorkers := 2 // 测试时使用较少的工作线程
	log.Printf("启动 %d 个测试挖矿线程，运行时间: %v", numWorkers, duration)

	// 启动统计打印器
	go m.startTestStatsReporter(duration)

	var wg sync.WaitGroup
	stopChan := make(chan bool, numWorkers)

	// 设置停止定时器
	go func() {
		time.Sleep(duration)
		for i := 0; i < numWorkers; i++ {
			stopChan <- true
		}
	}()

	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			m.testMiningWorker(workerID, numWorkers, stopChan)
		}(i)
	}
	wg.Wait()
	log.Printf("测试挖矿完成")
}

func (m *Miner) miningWorker(workerID, numWorkers int) {
	startNonce := uint32(workerID)
	step := uint32(numWorkers)
	localHashCount := uint64(0)

	for {
		if m.currentJob == nil {
			time.Sleep(100 * time.Millisecond)
			continue
		}

		header := m.buildBlockHeader(startNonce)
		if header == nil {
			continue
		}

		hash := m.calculateHash(header)
		localHashCount++

		// 每1000个哈希更新一次统计
		if localHashCount%1000 == 0 {
			m.stats.recordHash(1000)
		}

		if m.checkDifficulty(hash) {
			log.Printf("Worker %d found share - Nonce: %08x, ExtraNonce2: %s", workerID, startNonce, m.extraNonce2)
			m.submitShare(startNonce)
			m.stats.recordShare()
		}

		startNonce += step
		if startNonce == 0 {
			m.incrementExtraNonce2()
			log.Printf("Incrementing ExtraNonce2 to: %s", m.extraNonce2)
		}
	}
}

func (m *Miner) startStatsReporter() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		m.stats.printStats()
	}
}

// startTestStatsReporter 启动测试统计报告器
func (m *Miner) startTestStatsReporter(duration time.Duration) {
	ticker := time.NewTicker(10 * time.Second) // 测试时更频繁的报告
	defer ticker.Stop()

	timeout := time.After(duration)

	for {
		select {
		case <-ticker.C:
			m.stats.printTestStats()
		case <-timeout:
			log.Printf("测试统计报告器停止")
			return
		}
	}
}

// testMiningWorker 测试挖矿工作线程
func (m *Miner) testMiningWorker(workerID, numWorkers int, stopChan <-chan bool) {
	startNonce := uint32(workerID)
	step := uint32(numWorkers)
	localHashCount := uint64(0)

	log.Printf("测试工作线程 %d 开始", workerID)

	for {
		select {
		case <-stopChan:
			log.Printf("测试工作线程 %d 停止，处理了 %d 个哈希", workerID, localHashCount)
			return
		default:
			if m.currentJob == nil {
				time.Sleep(100 * time.Millisecond)
				continue
			}

			header := m.buildBlockHeader(startNonce)
			if header == nil {
				continue
			}

			hash := m.calculateHash(header)
			localHashCount++

			// 每100个哈希更新一次统计（测试时减少频率）
			if localHashCount%100 == 0 {
				m.stats.recordHash(100)
			}

			if m.checkDifficulty(hash) {
				log.Printf("测试工作线程 %d 找到份额 - Nonce: %08x", workerID, startNonce)
				m.submitShare(startNonce)
				m.stats.recordShare()
			}

			startNonce += step
			if startNonce == 0 {
				m.incrementExtraNonce2()
			}
		}
	}
}

func (s *MiningStats) recordHash(count uint64) {
	s.mutex.Lock()
	s.hashCount += count
	s.mutex.Unlock()
}

func (s *MiningStats) recordShare() {
	s.mutex.Lock()
	s.shareCount++
	s.submissions++
	s.mutex.Unlock()
}

func (s *MiningStats) printStats() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	duration := time.Since(s.startTime).Seconds()
	hashrate := float64(s.hashCount) / duration
	sharerate := float64(s.shareCount) / duration
	acceptRate := float64(s.accepts) / float64(s.submissions) * 100

	log.Printf("Mining Stats:\n"+
		"Hashrate: %.2f H/s\n"+
		"Shares: %.2f shares/s\n"+
		"Accept Rate: %.2f%%\n"+
		"Accepted: %d, Rejected: %d\n",
		hashrate, sharerate, acceptRate,
		s.accepts, s.rejects)
}

// printTestStats 打印测试统计信息
func (s *MiningStats) printTestStats() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	duration := time.Since(s.startTime).Seconds()
	hashrate := float64(s.hashCount) / duration

	log.Printf("Test Mining Stats: Hashrate: %.2f H/s, Hashes: %d, Shares: %d, Duration: %.1fs",
		hashrate, s.hashCount, s.shareCount, duration)
}

func (m *Miner) buildBlockHeader(nonce uint32) []byte {
	if m.currentJob == nil {
		return nil
	}

	version, err := hex.DecodeString(m.currentJob.Version)
	if err != nil {
		log.Printf("Invalid version format: %v", err)
		return nil
	}

	prevHash, err := hex.DecodeString(m.currentJob.PrevHash)
	if err != nil {
		log.Printf("Invalid prevHash format: %v", err)
		return nil
	}

	nbits, err := hex.DecodeString(m.currentJob.Nbits)
	if err != nil {
		log.Printf("Invalid nbits format: %v", err)
		return nil
	}

	ntime, err := hex.DecodeString(m.currentJob.Ntime)
	if err != nil {
		log.Printf("Invalid ntime format: %v", err)
		return nil
	}

	header := new(bytes.Buffer)
	header.Write(version)
	header.Write(prevHash)
	header.Write(m.calculateMerkleRoot(m.buildCoinbase()))
	header.Write(ntime)
	header.Write(nbits)
	binary.Write(header, binary.LittleEndian, nonce)

	return header.Bytes()
}

func (m *Miner) calculateHash(header []byte) []byte {
	// Scrypt 参数
	N := 1024    // CPU/memory cost parameter
	r := 1       // block size parameter
	p := 1       // parallelization parameter
	keyLen := 32 // output length

	// 使用空盐值（salt），因为在挖矿场景下不需要额外的盐值
	salt := header

	hash, err := scrypt.Key(header, salt, N, r, p, keyLen)
	if err != nil {
		log.Printf("计算 scrypt hash 失败: %v", err)
		return nil
	}

	return hash
}

func (m *Miner) checkDifficulty(hash []byte) bool {
	// 检查哈希是否满足难度要求
	target := calculateTarget(m.difficulty)
	return bytes.Compare(hash, target) <= 0
}

func (m *Miner) submitShare(nonce uint32) {
	req := StratumRequest{
		ID:     4,
		Method: "mining.submit",
		Params: []interface{}{
			Worker,
			m.currentJob.JobID,
			m.extraNonce2,
			m.currentJob.Ntime,
			fmt.Sprintf("%08x", nonce),
		},
	}

	log.Printf("提交份额: JobID=%s, Nonce=%08x, ExtraNonce2=%s",
		m.currentJob.JobID,
		nonce,
		m.extraNonce2,
	)

	if err := m.encoder.Encode(req); err != nil {
		log.Printf("提交份额失败: %v", err)
	}
}

func (m *Miner) incrementExtraNonce2() {
	// 将 extraNonce2 转换为整数并增加
	value, _ := hex.DecodeString(m.extraNonce2)
	num := binary.LittleEndian.Uint32(value)
	num++

	// 转回十六进制字符串
	binary.LittleEndian.PutUint32(value, num)
	m.extraNonce2 = hex.EncodeToString(value)
}

func (m *Miner) buildCoinbase() []byte {
	coinbase1, err := hex.DecodeString(m.currentJob.Coinbase1)
	if err != nil {
		log.Printf("Invalid coinbase1 format: %v", err)
		return nil
	}

	extraNonce1, err := hex.DecodeString(m.extraNonce1)
	if err != nil {
		log.Printf("Invalid extraNonce1 format: %v", err)
		return nil
	}

	extraNonce2, err := hex.DecodeString(m.extraNonce2)
	if err != nil {
		log.Printf("Invalid extraNonce2 format: %v", err)
		return nil
	}

	coinbase2, err := hex.DecodeString(m.currentJob.Coinbase2)
	if err != nil {
		log.Printf("Invalid coinbase2 format: %v", err)
		return nil
	}

	coinbase := new(bytes.Buffer)
	coinbase.Write(coinbase1)
	coinbase.Write(extraNonce1)
	coinbase.Write(extraNonce2)
	coinbase.Write(coinbase2)

	return coinbase.Bytes()
}

func (m *Miner) calculateMerkleRoot(coinbase []byte) []byte {
	// 计算 coinbase 的哈希
	hash := doubleSHA256(coinbase)

	// 使用 merkle 分支计算 merkle root
	for _, h := range m.currentJob.MerkleBranch {
		branch, _ := hex.DecodeString(h)
		hash = doubleSHA256(append(hash, branch...))
	}

	return hash
}

func doubleSHA256(data []byte) []byte {
	hash1 := sha256.Sum256(data)
	hash2 := sha256.Sum256(hash1[:])
	return hash2[:]
}

func calculateTarget(difficulty float64) []byte {
	// 难度 1 对应的初始目标值 (0x00000000ffff0000000000000000000000000000000000000000000000000000)
	pdiff := new(big.Int).SetBytes([]byte{
		0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	})

	diff := big.NewFloat(difficulty)

	// 计算实际目标值 = pdiff / difficulty
	target := new(big.Float).Quo(new(big.Float).SetInt(pdiff), diff)

	targetInt, _ := target.Int(nil)

	// 转换为字节数组，保持32字节长度
	targetBytes := make([]byte, 32)
	targetIntBytes := targetInt.Bytes()
	copy(targetBytes[32-len(targetIntBytes):], targetIntBytes)

	return targetBytes
}
