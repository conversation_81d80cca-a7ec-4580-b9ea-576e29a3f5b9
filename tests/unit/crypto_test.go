package tests

import (
	"testing"

	"dogepool/blockchain"
)

// https://litecoin.info/docs/key-concepts/proof-of-work
// https://api.blockchair.com/litecoin/raw/block/29255
func TestScryptDigest(t *testing.T) {

	input := "01000000f615f7ce3b4fc6b8f61e8f89aedb1d0852507650533a9e3b10b9bbcc30639f279fcaa86746e1ef52d3edb3c4ad8259920d509bd073605c9bf1d59983752a6b06b817bb4ea78e011d012d59d4"

	got, err := blockchain.ScryptDigestLE(input)

	if err != nil {
		t.<PERSON><PERSON><PERSON>("ScryptDigest() error = %v", err)
		return
	}

	if len(got) != 64 {
		t.<PERSON><PERSON><PERSON>("ScryptDigest() output length = %v, want 64", len(got))
	}
}

func TestDoubleSha256(t *testing.T) {
	input := "01000000f615f7ce3b4fc6b8f61e8f89aedb1d0852507650533a9e3b10b9bbcc30639f279fcaa86746e1ef52d3edb3c4ad8259920d509bd073605c9bf1d59983752a6b06b817bb4ea78e011d012d59d4"
	got, err := blockchain.DoubleSha256LE(input)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("ScryptDigest() error = %v", err)
		return
	}
	if got != "adf6e2e56df692822f5e064a8b6404a05d67cccd64bc90f57f65b46805e9a54b" {
		t.<PERSON><PERSON><PERSON>("DoubleSha256()%v", got)
	}

}
