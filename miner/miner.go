package main

import (
	"bytes"
	"crypto/sha256"
	"encoding/binary"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"golang.org/x/crypto/scrypt"
	"log"
	"math/big"
	"net"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"
)

// 常量定义
var (
	PoolAddress          = getEnvOrDefault("POOL_ADDRESS", "localhost:3643")
	Worker               = getEnvOrDefault("WORKER_NAME", "primarycoinAddress-auxcoinAddress.worker1")
	RetryInterval        = 5 * time.Second
	DebugMode            = os.Getenv("DEBUG") == "1"
	TRANSACTION_COINBASE = 5
)

type StratumRequest struct {
	ID     int           `json:"id"`
	Method string        `json:"method"`
	Params []interface{} `json:"params"`
}

type StratumResponse struct {
	ID     int           `json:"id"`
	Result interface{}   `json:"result,omitempty"`
	Error  interface{}   `json:"error,omitempty"`
	Method string        `json:"method,omitempty"`
	Params []interface{} `json:"params,omitempty"`
}

type MiningJob struct {
	JobID           string
	PrevHash        string
	Coinbase1       string
	Coinbase2       string
	MerkleBranch    []string
	Version         string
	Nbits           string
	Ntime           string
	CleanJobs       bool
	Height          int64
	WitnessVersion  byte
	WitnessReserved bool
	IsMWEBEnabled   bool
	CoinbasePayload string
}

type MiningStats struct {
	hashCount     uint64
	shareCount    uint64
	startTime     time.Time
	submissions   uint64
	accepts       uint64
	rejects       uint64
	lastShareTime time.Time
	shareInterval time.Duration
	mutex         sync.Mutex
}

type Miner struct {
	conn        net.Conn
	encoder     *json.Encoder
	decoder     *json.Decoder
	extraNonce1 string
	extraNonce2 string
	difficulty  float64
	currentJob  *MiningJob
	stats       *MiningStats
	height      int64
	mutex       sync.Mutex
}

func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func main() {
	log.SetFlags(log.Ldate | log.Ltime | log.Lmicroseconds)
	startMiner()
}

func startMiner() {
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigChan
		log.Println("正在关闭矿工...")
		os.Exit(0)
	}()

	for {
		if err := runMiner(); err != nil {
			log.Printf("Mining error: %v, retrying in %v...", err, RetryInterval)
			time.Sleep(RetryInterval)
			continue
		}
		log.Printf("矿工会话结束，准备重新连接...")
	}
}

func runMiner() error {
	conn, err := net.Dial("tcp", PoolAddress)
	if err != nil {
		return fmt.Errorf("连接失败: %v", err)
	}
	defer conn.Close()

	miner := &Miner{
		conn:    conn,
		encoder: json.NewEncoder(conn),
		decoder: json.NewDecoder(conn),
		stats: &MiningStats{
			startTime: time.Now(),
		},
	}

	if err := miner.subscribe(); err != nil {
		return fmt.Errorf("订阅失败: %v", err)
	}

	if err := miner.authorize(); err != nil {
		return fmt.Errorf("认证失败: %v", err)
	}

	//go miner.startHeartbeat()
	go miner.startMining()
	miner.handleServerMessages()
	return nil
}

func (m *Miner) subscribe() error {
	req := StratumRequest{
		ID:     1,
		Method: "mining.subscribe",
		Params: []interface{}{"test-miner/1.0.0"},
	}

	if err := m.encoder.Encode(req); err != nil {
		return err
	}

	var resp StratumResponse
	if err := m.decoder.Decode(&resp); err != nil {
		return err
	}

	if result, ok := resp.Result.([]interface{}); ok {
		if len(result) > 1 {
			m.extraNonce1 = result[1].(string)
			m.extraNonce2 = "00000000" // 初始化 extraNonce2
			log.Printf("已初始化 ExtraNonce1: %s, ExtraNonce2: %s",
				m.extraNonce1, m.extraNonce2)
		}
	}

	return nil
}

func (m *Miner) authorize() error {
	req := StratumRequest{
		ID:     2,
		Method: "mining.authorize",
		Params: []interface{}{Worker, "x"},
	}

	if err := m.encoder.Encode(req); err != nil {
		return err
	}

	var resp StratumResponse
	if err := m.decoder.Decode(&resp); err != nil {
		return err
	}

	if resp.Error != nil {
		return fmt.Errorf("授权失败: %v", resp.Error)
	}

	log.Printf("授权成功")
	return nil
}

func (m *Miner) startHeartbeat() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		req := StratumRequest{
			ID:     3,
			Method: "mining.ping",
			Params: []interface{}{},
		}

		if err := m.encoder.Encode(req); err != nil {
			log.Printf("发送心跳失败: %v", err)
			return
		}
	}
}

func (m *Miner) handleServerMessages() {
	for {
		var msg StratumResponse
		if err := m.decoder.Decode(&msg); err != nil {
			if err.Error() == "EOF" {
				log.Printf("连接断开，准备重连")
				return
			}
			log.Printf("读取服务器消息失败: %v", err)
			continue
		}

		switch msg.Method {
		case "mining.notify":
			m.handleNewJob(msg.Params)
		case "mining.set_difficulty":
			if len(msg.Params) > 0 {
				m.difficulty = msg.Params[0].(float64)
				target := calculateTarget(m.difficulty)
				if DebugMode {
					log.Printf("难度已更新为: %f (target: %x)",
						m.difficulty, target[:8])
				}
			}
		default:
			if msg.ID == 4 { // submit share response
				if msg.Error == nil {
					m.stats.mutex.Lock()
					m.stats.accepts++
					m.stats.mutex.Unlock()
					log.Printf("份额被接受")
				} else {
					m.stats.mutex.Lock()
					m.stats.rejects++
					m.stats.mutex.Unlock()
					log.Printf("份额被拒绝: %v", msg.Error)
				}
			}
		}
	}
}

func (m *Miner) handleNewJob(params []interface{}) {
	// 首先打印接收到的参数，帮助调试
	if DebugMode {
		log.Printf("Received job params: %+v", params)
	}

	// 检查 params 是否为 nil
	if params == nil {
		log.Printf("收到空的任务参数")
		return
	}

	// 检查参数长度
	if len(params) < 9 {
		log.Printf("无效的任务参数: 需要 9 个参数，实际收到 %d 个", len(params))
		for i, p := range params {
			log.Printf("参数 %d: %T = %v", i, p, p)
		}
		return
	}

	// 使用安全的类型断言函数
	jobID := safeGetString(params, 0, "JobID")
	prevHash := safeGetString(params, 1, "PrevHash")
	coinbase1 := safeGetString(params, 2, "Coinbase1")
	coinbase2 := safeGetString(params, 3, "Coinbase2")
	version := safeGetString(params, 5, "Version")
	nbits := safeGetString(params, 6, "NBits")
	ntime := safeGetString(params, 7, "NTime")

	// 如果任何必需参数为空，则返回
	if jobID == "" || prevHash == "" || coinbase1 == "" ||
		coinbase2 == "" || version == "" || nbits == "" || ntime == "" {
		return
	}

	// 安全地获取 merkle 分支
	var merkleStrings []string
	if merkleBranch, ok := params[4].([]interface{}); ok {
		merkleStrings = make([]string, 0, len(merkleBranch))
		for i, v := range merkleBranch {
			if str, ok := v.(string); ok {
				merkleStrings = append(merkleStrings, str)
			} else {
				log.Printf("无效的 Merkle 分支元素 %d: %T = %v", i, v, v)
				return
			}
		}
	} else {
		log.Printf("无效的 MerkleBranch 格式: %T = %v", params[4], params[4])
		return
	}

	// 安全地获取 cleanJobs
	cleanJobs := false
	if len(params) > 8 {
		if clean, ok := params[8].(bool); ok {
			cleanJobs = clean
		}
	}

	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 尝试解析高度
	var height int64
	if h, err := m.extractHeightFromCoinbase(coinbase1); err == nil {
		height = h
		if m.height != height {
			log.Printf("区块高度更新: %d -> %d", m.height, height)
		}
		m.height = height
	} else {
		log.Printf("解析高度失败: %v，使用当前高度: %d", err, m.height)
		height = m.height
	}

	var coinbasePayload string
	if len(params) > 9 {
		if payload, ok := params[9].(string); ok {
			coinbasePayload = payload
			if DebugMode {
				log.Printf("Found coinbase payload: %s", payload)
			}
		}
	}

	// 创建新的任务
	m.currentJob = &MiningJob{
		JobID:           jobID,
		PrevHash:        prevHash,
		Coinbase1:       coinbase1,
		Coinbase2:       coinbase2,
		MerkleBranch:    merkleStrings,
		Version:         version,
		Nbits:           nbits,
		Ntime:           ntime,
		CleanJobs:       cleanJobs,
		Height:          height,
		CoinbasePayload: coinbasePayload,
	}

	if DebugMode {
		log.Printf("新任务创建成功:\n"+
			"  JobID: %s\n"+
			"  Height: %d\n"+
			"  Version: %s\n"+
			"  PrevHash: %s\n"+
			"  NBits: %s\n"+
			"  NTime: %s\n"+
			"  MerkleBranch: %d items\n"+
			"  CleanJobs: %v",
			jobID, height, version, prevHash,
			nbits, ntime, len(merkleStrings), cleanJobs)
	}
}

func (m *Miner) startMining() {
	//numWorkers := runtime.NumCPU()
	numWorkers := 1
	log.Printf("启动 %d 个挖矿线程", numWorkers)

	// 启动统计打印器
	go m.startStatsReporter()

	var wg sync.WaitGroup
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			m.miningWorker(workerID, numWorkers)
		}(i)
	}
	wg.Wait()
}

func (m *Miner) startStatsReporter() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		m.stats.printStats()
	}
}

func (m *Miner) miningWorker(workerID, numWorkers int) {
	startNonce := uint32(workerID)
	step := uint32(numWorkers)
	localHashCount := uint64(0)
	lastLogTime := time.Now()
	lastHashRate := time.Now()
	hashesThisPeriod := uint64(0)

	log.Printf("Worker %d started, difficulty: %f", workerID, m.difficulty)

	for {
		if m.currentJob == nil {
			time.Sleep(100 * time.Millisecond)
			continue
		}

		m.mutex.Lock()
		height := m.height
		m.mutex.Unlock()

		header := m.buildBlockHeader(startNonce)
		if header == nil {
			continue
		}

		hash := m.calculateHash(header)
		localHashCount++
		hashesThisPeriod++

		// 检查是否需要增加 extraNonce2
		if startNonce > uint32(0xFFFFFFFF-step) {
			m.incrementExtraNonce2()
			if DebugMode {
				log.Printf("Worker %d: nonce exhausted, incrementing ExtraNonce2 to %s",
					workerID, m.extraNonce2)
			}
		}

		if m.checkDifficulty(hash) {
			log.Printf("Worker %d found share! Hash: %x, Nonce: %08x, ExtraNonce2: %s",
				workerID, hash[:8], startNonce, m.extraNonce2)
			m.submitShare(startNonce)
			m.stats.recordShare()

			// 找到share后也增加extraNonce2
			m.incrementExtraNonce2()
			if DebugMode {
				log.Printf("Worker %d: share found, incrementing ExtraNonce2 to %s",
					workerID, m.extraNonce2)
			}
		}

		startNonce += step

		// 哈希率统计和日志输出
		if time.Since(lastHashRate) >= time.Second {
			hashrate := float64(hashesThisPeriod) / time.Since(lastHashRate).Seconds()
			if DebugMode {
				log.Printf("Worker %d hashrate: %.2f H/s, ExtraNonce2: %s",
					workerID, hashrate, m.extraNonce2)
			}
			hashesThisPeriod = 0
			lastHashRate = time.Now()
		}

		if time.Since(lastLogTime) >= 10*time.Second {
			zeros := countLeadingZeros(hash)
			if DebugMode {
				log.Printf("Worker %d stats:\n"+
					"  Hash: %x\n"+
					"  Target: %x\n"+
					"  Leading zeros: %d\n"+
					"  Difficulty: %f\n"+
					"  Height: %d\n"+
					"  ExtraNonce2: %s",
					workerID, hash[:8], calculateTarget(m.difficulty)[:8],
					zeros, m.difficulty, height, m.extraNonce2)
			}
			lastLogTime = time.Now()
		}

		// 每1000个哈希更新一次统计
		if localHashCount%1000 == 0 {
			m.stats.recordHash(1000)
		}
	}
}

func (m *Miner) buildBlockHeader(nonce uint32) []byte {
	if m.currentJob == nil {
		return nil
	}

	// 检查是否是 MWEB 区块
	if m.currentJob.IsMWEBEnabled {
		log.Printf("Warning: Attempting to mine MWEB block at height %d", m.height)
	}

	// 检查是否是 SegWit 激活高度
	isSegWit := m.height >= 431

	// 解码所有必要的字段
	version, err := hex.DecodeString(m.currentJob.Version)
	if err != nil {
		log.Printf("Invalid version format: %v", err)
		return nil
	}

	// 对于 SegWit 区块，设置 version 位
	if isSegWit {
		versionInt := binary.LittleEndian.Uint32(version)
		versionInt |= (1 << 29) // 设置 SegWit 标志位
		binary.LittleEndian.PutUint32(version, versionInt)
	}

	prevHash, err := hex.DecodeString(m.currentJob.PrevHash)
	if err != nil {
		log.Printf("Invalid prevHash format: %v", err)
		return nil
	}

	// 构建 coinbase 和 merkle root
	var coinbase []byte
	if isSegWit {
		//coinbase = m.buildCoinbaseWithSegwit()
		coinbase = m.buildCoinbaseTransaction()
	} else {
		coinbase = m.buildRegularCoinbase()
	}

	if coinbase == nil {
		return nil
	}

	merkleRoot := m.calculateMerkleRoot(coinbase)
	if merkleRoot == nil {
		return nil
	}

	ntime, err := hex.DecodeString(m.currentJob.Ntime)
	if err != nil {
		log.Printf("Invalid ntime format: %v", err)
		return nil
	}

	nbits, err := hex.DecodeString(m.currentJob.Nbits)
	if err != nil {
		log.Printf("Invalid nbits format: %v", err)
		return nil
	}

	// 构建区块头
	header := new(bytes.Buffer)
	header.Write(version)
	header.Write(prevHash)
	header.Write(merkleRoot)
	header.Write(ntime)
	header.Write(nbits)
	binary.Write(header, binary.LittleEndian, nonce)

	if DebugMode {
		log.Printf("Block header at height %d:\n"+
			"  Version: %x (SegWit: %v)\n"+
			"  PrevHash: %x\n"+
			"  MerkleRoot: %x\n"+
			"  Time: %x\n"+
			"  Bits: %x\n"+
			"  Nonce: %08x",
			m.height, version, isSegWit,
			prevHash, merkleRoot, ntime, nbits, nonce)
	}

	return header.Bytes()
}

func (m *Miner) buildCoinbaseTransaction() []byte {
	coinbase1, _ := hex.DecodeString(m.currentJob.Coinbase1)
	extraNonce1, _ := hex.DecodeString(m.extraNonce1)
	extraNonce2, _ := hex.DecodeString(m.extraNonce2)
	coinbase2, _ := hex.DecodeString(m.currentJob.Coinbase2)

	tx := new(bytes.Buffer)

	// 1. 版本号 (4 bytes) - 使用版本 3
	binary.Write(tx, binary.LittleEndian, uint32(3))

	// 2. 交易类型 (2 bytes)
	binary.Write(tx, binary.LittleEndian, uint16(TRANSACTION_COINBASE))

	// 3. 输入数量
	tx.WriteByte(0x01) // 1 input

	// 4. 输入
	// 4.1 空的前一个交易哈希
	tx.Write(make([]byte, 32))
	// 4.2 输出索引
	binary.Write(tx, binary.LittleEndian, uint32(0xFFFFFFFF))

	// 4.3 coinbase 脚本
	scriptSig := new(bytes.Buffer)
	scriptSig.Write(coinbase1)
	scriptSig.Write(extraNonce1)
	scriptSig.Write(extraNonce2)

	writeVarInt(tx, uint64(scriptSig.Len()))
	tx.Write(scriptSig.Bytes())

	// 4.4 序列号
	binary.Write(tx, binary.LittleEndian, uint32(0xFFFFFFFF))

	// 5. 输出
	if len(coinbase2) > 0 {
		tx.Write(coinbase2)
	} else {
		// 默认输出
		tx.WriteByte(0x01) // 1 output

		// 5.1 输出值
		binary.Write(tx, binary.LittleEndian, uint64(5000000000))

		// 5.2 输出脚本
		pkScript := []byte{
			0x76, // OP_DUP
			0xa9, // OP_HASH160
			0x14, // Push 20 bytes
		}
		pkScript = append(pkScript, make([]byte, 20)...)
		pkScript = append(pkScript,
			0x88, // OP_EQUALVERIFY
			0xac, // OP_CHECKSIG
		)

		writeVarInt(tx, uint64(len(pkScript)))
		tx.Write(pkScript)
	}

	// 6. 添加 coinbase payload
	if m.currentJob.CoinbasePayload != "" {
		payload, _ := hex.DecodeString(m.currentJob.CoinbasePayload)
		writeVarInt(tx, uint64(len(payload)))
		tx.Write(payload)
	}

	// 7. 锁定时间
	binary.Write(tx, binary.LittleEndian, uint32(0))

	if DebugMode {
		log.Printf("Coinbase transaction:\n"+
			"  Version: 3\n"+
			"  Type: %d (TRANSACTION_COINBASE)\n"+
			"  Size: %d bytes\n"+
			"  Has payload: %v",
			TRANSACTION_COINBASE,
			tx.Len(),
			m.currentJob.CoinbasePayload != "")
	}

	return tx.Bytes()
}

func (m *Miner) buildCoinbaseWithSegwit() []byte {
	coinbase1, _ := hex.DecodeString(m.currentJob.Coinbase1)
	extraNonce1, _ := hex.DecodeString(m.extraNonce1)
	extraNonce2, _ := hex.DecodeString(m.extraNonce2)
	coinbase2, _ := hex.DecodeString(m.currentJob.Coinbase2)

	// 构建完整的 coinbase 交易
	tx := new(bytes.Buffer)

	// 1. 版本号 (4 bytes)
	binary.Write(tx, binary.LittleEndian, uint32(2)) // version 2 for SegWit

	// 2. Segwit 标记和标志
	tx.Write([]byte{0x00, 0x01}) // marker = 0x00, flag = 0x01

	// 3. 输入数量 (varint)
	tx.WriteByte(0x01) // 1 input

	// 4. 输入
	// 4.1 空的前一个交易哈希 (32 bytes)
	tx.Write(make([]byte, 32))
	// 4.2 输出索引 (4 bytes)
	binary.Write(tx, binary.LittleEndian, uint32(0xFFFFFFFF))

	// 4.3 构建 coinbase 脚本
	scriptSig := new(bytes.Buffer)
	scriptSig.Write(coinbase1)
	scriptSig.Write(extraNonce1)
	scriptSig.Write(extraNonce2)

	// 写入脚本长度
	writeVarInt(tx, uint64(scriptSig.Len()))
	// 写入脚本
	tx.Write(scriptSig.Bytes())

	// 4.4 序列号 (4 bytes)
	binary.Write(tx, binary.LittleEndian, uint32(0xFFFFFFFF))

	// 5. 输出数量 (varint)
	if len(coinbase2) > 0 {
		tx.Write(coinbase2)
	} else {
		// 默认输出
		tx.WriteByte(0x02) // 2 outputs (coinbase + witness commitment)

		// 5.1 coinbase 输出
		// 输出值 (8 bytes) - 50 LTC in satoshis
		binary.Write(tx, binary.LittleEndian, uint64(5000000000))

		// 输出脚本
		pkScript := []byte{
			0x76, // OP_DUP
			0xa9, // OP_HASH160
			0x14, // Push 20 bytes
		}
		// 添加实际的公钥哈希
		pkScript = append(pkScript, make([]byte, 20)...)
		pkScript = append(pkScript,
			0x88, // OP_EQUALVERIFY
			0xac, // OP_CHECKSIG
		)

		// 写入脚本长度和脚本
		writeVarInt(tx, uint64(len(pkScript)))
		tx.Write(pkScript)

		// 5.2 witness commitment 输出
		// 输出值 (8 bytes) - 0 satoshis
		binary.Write(tx, binary.LittleEndian, uint64(0))

		// witness commitment 脚本
		witnessRoot := m.buildWitnessMerkleTree()
		commitment := calculateWitnessCommitment(witnessRoot)

		commitmentScript := []byte{
			0x6a,                   // OP_RETURN
			0x24,                   // Push 36 bytes
			0xaa, 0x21, 0xa9, 0xed, // Commitment header
		}
		commitmentScript = append(commitmentScript, commitment...)

		// 写入脚本长度和脚本
		writeVarInt(tx, uint64(len(commitmentScript)))
		tx.Write(commitmentScript)
	}

	// 6. Witness 数据
	// 6.1 空的见证数据 (coinbase witness 必须为空)
	tx.WriteByte(0x00)

	// 7. 锁定时间 (4 bytes)
	binary.Write(tx, binary.LittleEndian, uint32(0))

	if DebugMode {
		log.Printf("SegWit Coinbase transaction:\n"+
			"  Version: 2\n"+
			"  Size: %d bytes\n"+
			"  ScriptSig size: %d bytes\n"+
			"  Has witness commitment: %v\n"+
			"  Raw tx: %x",
			tx.Len(),
			scriptSig.Len(),
			len(coinbase2) == 0,
			tx.Bytes())
	}

	return tx.Bytes()
}

func writeVarInt(w *bytes.Buffer, n uint64) {
	if n < 0xfd {
		w.WriteByte(uint8(n))
	} else if n <= 0xffff {
		w.WriteByte(0xfd)
		binary.Write(w, binary.LittleEndian, uint16(n))
	} else if n <= 0xffffffff {
		w.WriteByte(0xfe)
		binary.Write(w, binary.LittleEndian, uint32(n))
	} else {
		w.WriteByte(0xff)
		binary.Write(w, binary.LittleEndian, n)
	}
}

func (m *Miner) buildRegularCoinbase() []byte {
	coinbase1, _ := hex.DecodeString(m.currentJob.Coinbase1)
	extraNonce1, _ := hex.DecodeString(m.extraNonce1)
	extraNonce2, _ := hex.DecodeString(m.extraNonce2)
	coinbase2, _ := hex.DecodeString(m.currentJob.Coinbase2)

	coinbase := new(bytes.Buffer)
	coinbase.Write(coinbase1)
	coinbase.Write(extraNonce1)
	coinbase.Write(extraNonce2)
	coinbase.Write(coinbase2)

	return coinbase.Bytes()
}

func calculateWitnessCommitment(witnessRoot []byte) []byte {
	witnessNonce := make([]byte, 32)
	hasher := sha256.New()
	hasher.Write(witnessRoot)
	hasher.Write(witnessNonce)
	hash1 := hasher.Sum(nil)

	hasher.Reset()
	hasher.Write(hash1)
	return hasher.Sum(nil)
}

func (m *Miner) buildWitnessMerkleTree() []byte {
	witnesses := [][]byte{make([]byte, 32)} // Coinbase witness hash (全零)
	return buildMerkleRoot(witnesses)
}

func (m *Miner) calculateHash(header []byte) []byte {
	N := 128     // CPU/memory cost parameter
	r := 1       // block size parameter
	p := 1       // parallelization parameter
	keyLen := 32 // output length

	hash, err := scrypt.Key(header, header, N, r, p, keyLen)
	if err != nil {
		log.Printf("计算 scrypt hash 失败: %v", err)
		return nil
	}

	return hash
}

func (m *Miner) checkDifficulty(hash []byte) bool {
	if hash == nil {
		return false
	}

	target := calculateTarget(m.difficulty)
	return bytes.Compare(hash, target) <= 0
}

func calculateTarget(difficulty float64) []byte {
	target := make([]byte, 32)

	if difficulty <= 0 {
		return target
	}

	// 对于 Scrypt，使用标准目标值
	maxTarget := []byte{
		0x00, 0x00, 0x00, 0xff, 0xff, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	}

	// 将最大目标值转换为big.Int
	maxTargetInt := new(big.Int).SetBytes(maxTarget)

	// 计算实际目标值
	diff := big.NewFloat(difficulty)
	targetFloat := new(big.Float).Quo(new(big.Float).SetInt(maxTargetInt), diff)
	targetInt, _ := targetFloat.Int(nil)

	// 确保结果是32字节
	targetBytes := targetInt.Bytes()
	if len(targetBytes) > 32 {
		targetBytes = targetBytes[len(targetBytes)-32:]
	}

	copy(target[32-len(targetBytes):], targetBytes)
	return target
}

func (m *Miner) submitShare(nonce uint32) {
	if m.currentJob == nil || m.extraNonce2 == "" {
		log.Printf("Cannot submit share: missing job or extraNonce2")
		return
	}

	req := StratumRequest{
		ID:     4,
		Method: "mining.submit",
		Params: []interface{}{
			Worker,
			m.currentJob.JobID,
			m.extraNonce2,
			m.currentJob.Ntime,
			fmt.Sprintf("%08x", nonce),
		},
	}

	log.Printf("提交份额: JobID=%s, Nonce=%08x, ExtraNonce2=%s",
		m.currentJob.JobID,
		nonce,
		m.extraNonce2,
	)

	if err := m.encoder.Encode(req); err != nil {
		log.Printf("提交份额失败: %v", err)
	}
}

func (m *Miner) extractHeightFromCoinbase(coinbase1 string) (int64, error) {
	data, err := hex.DecodeString(coinbase1)
	if err != nil {
		return 0, err
	}

	// 在 coinbase 中查找 BIP34 编码的区块高度
	// 通常在脚本开始处，格式为：
	// [script length] [height length] [height] [rest of script]
	if len(data) < 7 { // 最小长度检查
		return 0, fmt.Errorf("coinbase too short")
	}

	// 跳过第一个字节（脚本长度）
	scriptLen := int(data[0])
	if len(data) < scriptLen+1 {
		return 0, fmt.Errorf("invalid script length")
	}

	// 查找高度标记
	var height int64
	for i := 1; i < len(data)-4; i++ {
		// 检查是否是 3-byte 或 4-byte 的推送操作
		if data[i] == 0x03 || data[i] == 0x04 {
			heightBytes := make([]byte, 8)
			// 复制高度数据（小端序）
			copy(heightBytes, data[i+1:i+5])
			height = int64(binary.LittleEndian.Uint32(heightBytes))
			if height > 0 && height < 1000000 { // 合理性检查
				return height, nil
			}
		}
	}

	// 如果找不到合适的高度，尝试直接解析
	heightBytes := make([]byte, 8)
	copy(heightBytes, data[1:5]) // 跳过第一个字节
	height = int64(binary.LittleEndian.Uint32(heightBytes))

	if DebugMode {
		log.Printf("Extracted height data: %x", data[1:5])
		log.Printf("Parsed height: %d", height)
	}

	return height, nil
}

func (m *Miner) incrementExtraNonce2() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.extraNonce2 == "" {
		m.extraNonce2 = "00000000"
		return
	}

	value, err := hex.DecodeString(m.extraNonce2)
	if err != nil {
		log.Printf("解析 ExtraNonce2 失败: %v", err)
		m.extraNonce2 = "00000000"
		return
	}

	// 确保 value 长度为 4 字节
	if len(value) != 4 {
		newValue := make([]byte, 4)
		copy(newValue[4-len(value):], value)
		value = newValue
	}

	num := binary.LittleEndian.Uint32(value)
	num++

	binary.LittleEndian.PutUint32(value, num)
	m.extraNonce2 = hex.EncodeToString(value)

	if DebugMode {
		log.Printf("ExtraNonce2 incremented to: %s", m.extraNonce2)
	}
}

func (m *Miner) calculateMerkleRoot(coinbase []byte) []byte {
	if coinbase == nil {
		return nil
	}

	// 计算 coinbase 交易的哈希
	coinbaseHash := doubleSHA256(coinbase)

	// 如果没有 merkle 分支，直接返回 coinbase 哈希
	if len(m.currentJob.MerkleBranch) == 0 {
		return coinbaseHash
	}

	// 从 coinbase 哈希开始，依次计算 merkle root
	merkleRoot := coinbaseHash
	for _, h := range m.currentJob.MerkleBranch {
		branch, err := hex.DecodeString(h)
		if err != nil {
			log.Printf("Invalid merkle branch format: %v", err)
			return nil
		}

		// 连接当前哈希和分支哈希
		buf := make([]byte, 64)
		copy(buf, merkleRoot)
		copy(buf[32:], branch)

		// 计算下一级哈希
		merkleRoot = doubleSHA256(buf)
	}

	return merkleRoot
}

func buildMerkleRoot(hashes [][]byte) []byte {
	if len(hashes) == 0 {
		return make([]byte, 32)
	}

	for len(hashes) > 1 {
		if len(hashes)%2 == 1 {
			hashes = append(hashes, hashes[len(hashes)-1])
		}

		var nextLevel [][]byte
		for i := 0; i < len(hashes); i += 2 {
			hasher := sha256.New()
			hasher.Write(hashes[i])
			hasher.Write(hashes[i+1])
			hash1 := hasher.Sum(nil)

			hasher.Reset()
			hasher.Write(hash1)
			nextLevel = append(nextLevel, hasher.Sum(nil))
		}
		hashes = nextLevel
	}

	return hashes[0]
}

func safeGetString(params []interface{}, index int, paramName string) string {
	if index >= len(params) {
		log.Printf("参数索引越界: %s (index: %d, len: %d)",
			paramName, index, len(params))
		return ""
	}

	if params[index] == nil {
		log.Printf("参数为空: %s", paramName)
		return ""
	}

	str, ok := params[index].(string)
	if !ok {
		log.Printf("无效的参数类型 %s: 期望 string, 得到 %T",
			paramName, params[index])
		return ""
	}

	return str
}

func doubleSHA256(data []byte) []byte {
	hash1 := sha256.Sum256(data)
	hash2 := sha256.Sum256(hash1[:])
	return hash2[:]
}

// MiningStats 方法
func (s *MiningStats) recordHash(count uint64) {
	s.mutex.Lock()
	s.hashCount += count
	s.mutex.Unlock()
}

func (s *MiningStats) recordShare() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	now := time.Now()
	if !s.lastShareTime.IsZero() {
		s.shareInterval = now.Sub(s.lastShareTime)
	}
	s.lastShareTime = now
	s.shareCount++
	s.submissions++
}

func (s *MiningStats) printStats() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	duration := time.Since(s.startTime).Seconds()
	hashrate := float64(s.hashCount) / duration
	sharerate := float64(s.shareCount) / duration
	acceptRate := float64(s.accepts) / float64(s.submissions) * 100

	log.Printf("Mining Stats:\n"+
		"Hashrate: %.2f H/s\n"+
		"Shares: %.2f shares/s\n"+
		"Accept Rate: %.2f%%\n"+
		"Last Share Interval: %v\n"+
		"Accepted: %d, Rejected: %d\n",
		hashrate, sharerate, acceptRate,
		s.shareInterval,
		s.accepts, s.rejects)
}

func countLeadingZeros(hash []byte) int {
	count := 0
	for _, b := range hash {
		if b == 0 {
			count += 8
			continue
		}
		for i := uint(7); i < 8; i-- {
			if b&(1<<i) == 0 {
				count++
			} else {
				return count
			}
		}
		break
	}
	return count
}
