# syntax=docker/dockerfile:1.4
FROM golang:1.23.0 AS builder

WORKDIR /app

# 缓存依赖
COPY go.mod go.sum ./
RUN --mount=type=cache,target=/go/pkg/mod \
    go mod download

# 复制源码并编译
COPY . .
RUN --mount=type=cache,target=/root/.cache/go-build \
    --mount=type=cache,target=/go/pkg/mod \
    CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o miner-app ./miner/miner.go

# 运行阶段
FROM alpine:latest

RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/miner-app ./miner-app

ENV POOL_ADDRESS=localhost:3643
ENV WORKER_NAME=primarycoinAddress-auxcoinAddress.worker1

CMD ["./miner-app"]