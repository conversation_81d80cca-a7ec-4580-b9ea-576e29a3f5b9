#!/bin/bash

# Docker Compose 启动前的设置脚本
# 解决权限和目录创建问题

set -e

echo "🚀 Setting up Docker environment..."

# 创建必要的目录结构
echo "📁 Creating directory structure..."
mkdir -p ltcdata/node1/db ltcdata/node2/db
mkdir -p dogedata/node1/db dogedata/node2/db
mkdir -p persistence/schema

# 为 ops 目录下的 compose 文件创建目录（如果不存在）
mkdir -p ops/ltcdata/node1/db ops/ltcdata/node2/db
mkdir -p ops/dogedata/node1/db ops/dogedata/node2/db

# 设置目录权限（如果需要）
echo "🔐 Setting directory permissions..."
chmod -R 755 ltcdata/ dogedata/ persistence/ || true

# 复制配置文件（如果存在）
echo "📋 Copying configuration files..."
if [ -f "ltcdata/litecoin.conf" ]; then
    cp ltcdata/litecoin.conf ltcdata/node1/ || true
    cp ltcdata/litecoin.conf ltcdata/node2/ || true
    echo "✅ Litecoin config files copied"
fi

if [ -f "dogedata/dogecoin.conf" ]; then
    cp dogedata/dogecoin.conf dogedata/node1/ || true
    cp dogedata/dogecoin.conf dogedata/node2/ || true
    echo "✅ Dogecoin config files copied"
fi

# 复制脚本文件到项目根目录（为了 docker-compose 路径解析）
echo "📋 Copying script files..."
if [ -d "ops/scripts" ]; then
    cp -r ops/scripts . || true
    chmod +x scripts/*.sh || true
    echo "✅ Script files copied and made executable"
fi

# 检查 Docker 和 docker-compose
echo "🐳 Checking Docker environment..."
docker --version
docker-compose --version

echo "✅ Setup completed! Ready to start docker-compose."
